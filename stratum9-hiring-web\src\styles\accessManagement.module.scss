@use "./abstracts" as *;

.access_management {
  :global(.margin-add) {
    margin-right: 70px;
    @media (max-width: 991px) {
      margin-right: 0;
    }
  }
  .user_roles_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    .role_left {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .role_right {
      width: 30%;
    }
    button,
    input {
      min-height: 45px;
    }
    @media (max-width: 576px) {
      flex-direction: column;
      align-items: flex-start;
      .role_right {
        width: 100%;
      }
      button,
      input {
        min-height: 40px;
      }
    }
  }
  .user_roles {
    @extend %listSpacing;
    li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px dashed $dark;
      padding-block: 20px;
      &:last-child {
        border-bottom: none;
        padding-bottom: 0;
      }
      button {
        svg {
          width: 20px;
          height: 20px;
        }
      }

      &.disabled_role {
        opacity: 0.75;
        p {
          color: rgba($dark, 0.7);
        }
        button.disabled {
          cursor: not-allowed;
          opacity: 0.6;
          svg {
            fill-opacity: 0.5;
            stroke-opacity: 0.5;
          }
        }
      }
    }
  }

  .user_roles_img {
    width: 100%;
    height: 500px;
    object-fit: contain;
    @media (max-width: 576px) {
      height: 300px;
      margin-top: 30px;
      padding: 0 10px;
    }
  }
  .role_select_employee {
    color: $primary;
    background-color: transparent;
  }

  .tip_para {
    font-size: 1.4rem;
    font-weight: 500;
    color: $dark;
    margin-bottom: 25px;
  }

  .add_employee_height {
    .form_card {
      border-radius: 30px;
      border: 2px solid rgba($dark, 0.2);
      padding: 20px;
      margin: 30px 0 20px 0;
    }
  }

  // Department card styles
  .folder_container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-3px);
    }
  }

  .department_card {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    padding: 0 8px;
    text-align: center;
    margin-bottom: 0;
    margin-top: 8px;
  }

  // Dropdown menu styles
  .custom_dropdown {
    position: absolute;
    z-index: 1000;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 220px; // Increased width to fit longer text
    padding: 8px 0;
    overflow: hidden;
    right: 0;
    bottom: calc(100% + 5px); // Position above by default for last rows

    @media (min-height: 768px) {
      &.show_below {
        top: 25px;
        bottom: auto;
      }
    }
  }

  .dropdown_item {
    padding: 12px 16px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  .dropdown_divider {
    height: 1px;
    background-color: #eee;
    margin: 0 8px;
  }

  // Role selector styles
  .role_selector {
    position: relative;
    display: inline-block;
    min-width: 180px;
  }

  .role_select {
    color: $primary;
    background-color: transparent;
    font-size: 14px;
    min-width: 220px;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23718096' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding: 0;
    cursor: pointer;
    border: none;

    &:focus {
      outline: none;
    }

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }

    option {
      background-color: white;
      color: #333;
      padding: 8px;
    }
  }

  .selected_role {
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    background-color: #f5f7fa;
    border: 1px solid #e2e8f0;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    transition: all 0.2s ease;
    width: 100%;

    &:hover {
      background-color: #edf2f7;
      border-color: #cbd5e0;
    }

    .dropdown_arrow {
      margin-left: 8px;
      font-size: 10px;
      color: #718096;
    }
  }

  .role_dropdown {
    position: absolute;
    left: 0;
    z-index: 1000;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 100%;
    min-width: 200px;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    animation: fadeIn 0.2s ease;
    bottom: calc(100% + 5px); // Position above by default for last rows

    &.show_below {
      top: calc(100% + 5px);
      bottom: auto;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-5px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .role_option {
    padding: 10px 15px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f8fafc;
    }

    &.selected {
      background-color: #ebf8ff;
      color: #3182ce;
      font-weight: 500;
    }

    .checkmark {
      color: #38b2ac;
      font-weight: bold;
      margin-left: 8px;
    }
  }
}

.subscription_page {
  padding-bottom: 80px;
  .subscription_plan_card {
    border-radius: 16px;
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.06);
    // padding: 15px;
    transition: all 0.3s ease;
    cursor: pointer;

    &.selected_plan {
      .subscription_option {
        background: rgba($secondary, 0.09);
      }
    }
  }
  .subscription_plan {
    @extend %listSpacing;
    max-height: 820px;
    overflow: auto;
    &.side_bar {
      box-shadow: none;
      padding: 15px 0;
      cursor: default;
      margin-top: 122px;
      max-height: max-content;
      li {
        display: flex;
        align-items: center;
        gap: 10px;
        justify-content: flex-start;
        text-align: left;
        padding-left: 10px;

        &.fixed_height {
          justify-content: flex-start;
        }

        .benefit_text {
          max-width: calc(100% - 40px);
        }
      }
      &:hover {
        transition: none;
        transform: translateY(0px);
      }
    }
    &.subscription_benefit_text {
      li {
        font-size: 1.5rem !important;
      }
    }
    li {
      font-size: $text-md;
      font-weight: $medium;
      color: $dark;
      padding: 10px 0;
      border-bottom: 1px solid rgba($primary, 0.2);
      text-align: center;
      min-height: 45px;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      &:last-child {
        border-bottom: none;
      }
      &.sibling_height {
        min-height: 65px;
      }
      &.fixed_height {
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        &.max_height {
          height: 65px;
          padding: 10px;
        }
      }

      &.description_cell {
        height: auto;
        min-height: 80px;
        max-height: 80px;
      }
      &.plan_name {
        font-size: $text-lg;
        font-weight: $bold;
        color: $dark;
        height: auto;
      }
      &.subscription_benefit_text {
        font-size: $text-xl;
        font-weight: $bold;
        color: $dark;
        height: auto;
        margin-bottom: 20px;
        display: block;
        border-bottom: 0;
      }
      svg {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        flex-shrink: 0;
      }
      .benefit_text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 180px;
      }
    }
  }
  .select_plan_section {
    margin-top: 30px;
    h3 {
      font-size: $text-lg;
      font-weight: $bold;
      color: $dark;
      margin-bottom: 20px;
    }
    .select_plan {
      border-radius: 16px;
      border: 1px solid rgba(#d8d8d8, 0.2);
      background: $white;
      box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.06);
      padding: 10px 20px;
      min-height: 75px;
      display: flex;
      align-items: center;
      cursor: pointer;
      h4 {
        font-size: $text-lg;
        font-weight: $semiBold;
        color: $dark;
        margin: 0;
      }
      p {
        font-size: 11px;
        font-weight: $regular;
        color: $dark;
        margin-top: 5px;
      }
      span {
        font-size: $text-md;
        font-weight: $medium;
        color: $primary;
      }
      &.flex_content {
        gap: 10px;
        justify-content: space-between;
      }
      &.active,
      &:hover {
        transition: all 0.5s ease;
        background: #ebf2ff;
        border-color: #ebf2ff;
        box-shadow: none;
      }
    }
  }

  .section_title {
    font-size: $text-lg;
    font-weight: $bold;
    color: $dark;
    margin-bottom: 15px;
  }

  .plan_option {
    flex: 1;
    padding: 15px;
    text-align: center;
    border-radius: 8px;
    border: 1px solid rgba($primary, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: $medium;
    font-size: $text-md;

    // &.selected {
    //   background-color: $primary;
    //   color: $white;
    //   border-color: $primary;

    //   &:hover {
    //     color: $white;
    //   }
    // }

    // &:hover:not(.selected) {
    //   border-color: $primary;
    //   color: $primary;
    // }
  }

  // card header style css
  .subscription_option {
    // flex: 0 0 auto;
    padding: 20px 15px;
    border-radius: 16px 16px 0px 0px;
    background: rgba($primary, 0.09);
    position: relative;
    text-align: center;
    min-height: 205px;

    &:last-child {
      margin-right: 0;
    }
    .plan_name {
      font-size: 2rem;
      font-weight: $bold;
      margin-bottom: 20px;
      margin-top: 10px;
    }
    .plan_price {
      font-size: 2.8rem;
      font-weight: $bold;
      color: $dark;
      margin-bottom: 0px;
    }
    .price_type {
      font-size: $text-xs;
      font-weight: $semiBold;
      color: rgba($dark, 0.6);
      margin-bottom: 0px;
      margin-top: 5px;
    }
    button {
      padding: 8px;
      border-radius: 16px;
      width: 100%;
      margin-top: 15px;
    }
    .save_badge {
      position: absolute;
      top: -12px;
      right: 0;
      left: 0;
      background-color: $secondary;
      color: $white;
      padding: 5px 8px;
      border-radius: 8px;
      font-size: $text-xs;
      font-weight: $medium;
      max-width: 110px;
      margin: auto;
      z-index: 100;
    }
  }
}
