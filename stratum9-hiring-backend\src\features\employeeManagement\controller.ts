import { Request, Response } from "express";
import * as Sentry from "@sentry/node";
import EmployeeManagementServices from "./services";
/**
 * Update employee status
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const updateEmployeeStatus = async (req: Request, res: Response) => {
  try {
    const { employeeId } = req.params;
    const { status } = req.body;
    const data = await EmployeeManagementServices.updateEmployeeStatus(
      +employeeId,
      { status, organizationId: req.orgId }
    );

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};
// ...existing code...
/**
 * Get list of departments
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const getDepartments = async (req: Request, res: Response) => {
  try {
    const { search } = req.query;
    const searchTerm = search ? (search as string) : undefined;

    const data = await EmployeeManagementServices.getDepartments(
      req.orgId,
      searchTerm
    );

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Add a new department
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const addDepartment = async (req: Request, res: Response) => {
  try {
    const data = await EmployeeManagementServices.addDepartment({
      ...req.body,
      organizationId: req.orgId,
    });

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Update a department
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const updateDepartment = async (req: Request, res: Response) => {
  try {
    const { departmentId } = req.params;
    const data = await EmployeeManagementServices.updateDepartment(
      +departmentId,
      { ...req.body, organizationId: req.orgId }
    );

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Delete a department
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const deleteDepartment = async (req: Request, res: Response) => {
  try {
    const { departmentId } = req.params;

    const data = await EmployeeManagementServices.deleteDepartment(
      +departmentId,
      req.orgId
    );
    return res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    return res.status(error.output?.statusCode ?? 500).json({
      success: false,
      message: "Internal Server Error",
      code: 500,
    });
  }
};

/**
 * Get employees by department
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const getEmployeesByDepartment = async (req: Request, res: Response) => {
  try {
    // Parse and convert query parameters to proper types
    // Support both page-based and offset-based pagination
    const { departmentId, search = "", limit, offset } = req.query;

    // Construct properly typed query params object
    const queryParams = {
      departmentId: +departmentId,
      organizationId: req.orgId,
      search: search as string,
      limit: +limit,
      offset: +offset,
    };

    const data =
      await EmployeeManagementServices.getEmployeesByDepartment(queryParams);

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Add employees
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const addEmployees = async (req: Request, res: Response) => {
  try {
    const data = await EmployeeManagementServices.addHireEmployees(
      {
        ...req.body,
      },
      req.orgId,
      req.userId
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Update employee role
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const updateEmployeeRole = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };
    const { employeeId } = req.params;

    const data = await EmployeeManagementServices.updateEmployeeRole(
      +employeeId,
      { ...body, organizationId: req.orgId }
    );

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Delete an employee
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const deleteEmployee = async (req: Request, res: Response) => {
  try {
    const { employeeId } = req.params;

    const data = await EmployeeManagementServices.deleteEmployee(
      +employeeId,
      req.orgId
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Add employees with user creation functionality
 * This is based on the hire employee registration feature
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const addHireEmployees = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await EmployeeManagementServices.addHireEmployees(
      body,
      req.orgId,
      req.userId
    );

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get departments by organization ID
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const getDepartmentsByOrganizationId = async (
  req: Request,
  res: Response
) => {
  try {
    const data =
      await EmployeeManagementServices.getDepartmentsByOrganizationId(
        req.orgId
      );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Update employee interview order
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const updateEmployeeInterviewOrder = async (
  req: Request,
  res: Response
) => {
  try {
    const { employeeId } = req.params;
    const { departmentId, newInterviewOrder } = req.body;
    const data = await EmployeeManagementServices.updateEmployeeInterviewOrder({
      newInterviewOrder,
      organizationId: req.orgId,
      employeeId: +employeeId,
      departmentId,
    });

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get employees by organization ID
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const getEmployeesByOrganizationId = async (
  req: Request,
  res: Response
) => {
  try {
    const { orgId } = req.query;
    console.log("orgId", orgId);
    const data = await EmployeeManagementServices.getEmployeesByOrganizationId(
      Number(orgId)
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};
