import { FindOptionsWhere, In } from "typeorm";
import * as Sen<PERSON> from "@sentry/node";
import DbConnection from "../../db/dbConnection";

import {
  ACCESS_MANAGEMENT_MSG,
  API_RESPONSE_MSG,
  DEFAULT_ORG_FIELDS,
  REDIS_EXPIRY,
  REDIS_KEYS,
} from "../../utils/constants";
import Cache from "../../db/cache";
import RoleModel from "../../schema/s9-innerview/roles";
import PermissionModel from "../../schema/s9-innerview/permissions";
import RolePermissionModel from "../../schema/s9-innerview/role_permissions_mapping";
import Employee from "../../schema/s9-innerview/employees";
import { IRolePermissionsUpdate, IUserRoleData } from "./interface";
import DepartmentModel from "../../schema/s9-innerview/departments";
import SubscriptionServices from "../subscriptions/services";

export class AccessManagementServices {
  /**
   * Get user roles list
   */

  static getCommonUserRoles = async (organizationId: number) => {
    try {
      const roleRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(RoleModel);

      const roles = await roleRepo.find({
        where: {
          isActive: true,
          organizationId,
        },
        select: {
          id: true,
          name: true,
          isDefaultRole: true,
        },
      });

      return {
        success: true,
        message: ACCESS_MANAGEMENT_MSG.user_roles_fetch,
        data: roles,
      };
    } catch (error) {
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
      };
    }
  };

  static getUserRolesPagination = async (data: any, organizationId: number) => {
    try {
      const roleRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(RoleModel);

      const { limit, offset } = data;

      const roles = await roleRepo
        .createQueryBuilder("role")
        .where({
          isActive: true,
          organizationId,
        })
        .select([
          "role.id as id",
          "role.name as name",
          "role.isDefaultRole as isDefaultRole",
        ])
        .orderBy("role.name", "ASC")
        .limit(limit)
        .offset(offset)
        .distinct(true)
        .getRawMany();

      return {
        success: true,
        message: ACCESS_MANAGEMENT_MSG.user_roles_fetch,
        data: roles,
      };
    } catch (error) {
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
      };
    }
  };

  /**
   * Add a new user role
   */
  static addUserRole = async (requestData: IUserRoleData) => {
    try {
      const { name, organizationId } = requestData;

      const roleRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(RoleModel);

      // Check if role with the same name already existsp0uj9oo9jui9ju9oju
      const existingRole = await roleRepo.findOne({
        where: {
          name,
          isActive: true,
          organizationId,
        },
      });

      if (existingRole) {
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.role_already_exists,
        };
      }

      // Create new role
      const newRole = new RoleModel();
      newRole.organizationId = organizationId;
      newRole.name = name;
      newRole.isActive = true;
      const savedRole = await roleRepo.save(newRole);

      return {
        success: true,
        message: ACCESS_MANAGEMENT_MSG.user_role_added,
        data: {
          id: savedRole.id,
          name: savedRole.name,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: ACCESS_MANAGEMENT_MSG.add_failed,
      };
    }
  };

  /**
   * Update an existing user role
   */
  static updateUserRole = async (
    roleId: string,
    requestData: IUserRoleData
  ) => {
    try {
      const { name, organizationId } = requestData;

      const roleRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(RoleModel);

      // Find the role to update
      const role = await roleRepo.findOne({
        where: {
          id: +roleId,
          isActive: true,
          organizationId,
        },
      });

      if (!role) {
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.role_not_found,
        };
      }
      if (role.isDefaultRole)
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.default_role_update_not_allowed,
        };

      // Check if another role with the same new name exists
      const existingRole = await roleRepo.findOne({
        where: {
          name,
          isActive: true,
          organizationId,
        } as FindOptionsWhere<RoleModel>,
      });

      if (existingRole && existingRole.id !== Number(roleId)) {
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.role_already_exists,
        };
      }

      // Update role
      role.name = name;
      const updatedRole = await roleRepo.save(role);

      return {
        success: true,
        message: ACCESS_MANAGEMENT_MSG.user_role_updated,
        data: {
          id: updatedRole.id,
          name: updatedRole.name,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: ACCESS_MANAGEMENT_MSG.update_failed,
      };
    }
  };

  /**
   * Delete a user role
   * @param roleId - ID of the role to delete
   * @param organizationId - ID of the organization
   */
  static deleteUserRole = async (roleId: string, organizationId: number) => {
    try {
      const roleRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(RoleModel);

      const role = await roleRepo.findOne({
        where: {
          id: Number(roleId),
          isActive: true,
          organizationId,
        },
      });

      if (!role) {
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.role_not_found,
        };
      }
      if (role.isDefaultRole)
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.default_role_update_not_allowed,
        };

      // Check if any employees are associated with this role
      const employeeRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(Employee);

      const employeesWithRole = await employeeRepo.count({
        where: {
          roleId: role.id,
          isActive: true,
        } as FindOptionsWhere<Employee>,
      });

      // If employees are associated with this role, prevent deletion
      if (employeesWithRole) {
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.role_has_employees,
        };
      }

      // Check if any permissions are assigned to this role
      const rolePermissionRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(
          RolePermissionModel
        );

      const rolePermissions = await rolePermissionRepo.find({
        where: {
          roleId: role.id,
        },
      });

      // If permissions are assigned to this role, delete them first
      if (rolePermissions.length > 0) {
        await rolePermissionRepo.delete({
          roleId: role.id,
        });
      }

      // Delete role since no employees are associated with it and permissions are cleared
      // Use the proper way to delete with TypeORM - delete by ID
      await roleRepo.delete(role.id);

      return {
        success: true,
        message: ACCESS_MANAGEMENT_MSG.user_role_deleted,
        data: {
          id: role.id,
          name: role.name,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: ACCESS_MANAGEMENT_MSG.delete_failed,
      };
    }
  };

  /**
   * Get role permissions
   * @param queryParams - Query parameters including search term
   */
  static getRolePermissions = async (queryParams: any) => {
    try {
      const { search, limit, offset, orgId } = queryParams;

      const roleRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(RoleModel);

      // Build query to get all roles, including those without permissions
      const roleQuery = roleRepo
        .createQueryBuilder("role")
        .leftJoin(
          RolePermissionModel,
          "rolePermission",
          "rolePermission.role_id = role.id"
        )
        .where("role.is_active = :isActive", { isActive: true })
        .andWhere("role.organizationId= :orgId", { orgId });

      // Add search condition if search parameter is provided
      if (search && search.trim() !== "") {
        roleQuery.andWhere("role.name LIKE :search", {
          search: `%${search}%`,
        });
      }

      // Select fields and count permissions
      roleQuery
        .select([
          "role.id as id",
          "role.name as name",
          "role.isDefaultRole as isDefaultRole",
          "COUNT(DISTINCT rolePermission.id) as permission_count",
          "COALESCE(MAX(rolePermission.updated_ts), role.updatedTs) as updated_ts",
        ])
        .groupBy("role.id");

      const roleList = await roleQuery.offset(offset).limit(limit).getRawMany();
      return {
        success: true,
        message: ACCESS_MANAGEMENT_MSG.role_permissions_fetch,
        data: roleList,
      };
    } catch (error) {
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
      };
    }
  };

  /**
   * Update role permissions
   */
  static updateRolePermissions = async (
    roleId: number,
    requestData: IRolePermissionsUpdate
  ) => {
    const dataSource = await DbConnection.getS9InnerviewDataSource();
    const queryRunner = dataSource.createQueryRunner();

    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();

      const { permissionIds = [], organizationId } = requestData;

      const roleRepo = queryRunner.manager.getRepository(RoleModel);
      const permissionRepo = queryRunner.manager.getRepository(PermissionModel);
      const rolePermissionRepo =
        queryRunner.manager.getRepository(RolePermissionModel);

      const role = await roleRepo.findOne({
        where: {
          id: roleId,
          isActive: true,
          organizationId,
        },
      });

      if (!role) {
        await queryRunner.rollbackTransaction();
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.role_not_found,
        };
      }
      if (role.isDefaultRole) {
        await queryRunner.rollbackTransaction();
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.default_role_update_not_allowed,
        };
      }

      if (permissionIds.length === 0) {
        await queryRunner.rollbackTransaction();
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.at_least_one_permission_required,
        };
      }

      const validPermissions = await permissionRepo.find({
        where: {
          id: In(permissionIds),
        },
      });

      if (!validPermissions.length) {
        await queryRunner.rollbackTransaction();
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.at_least_one_permission_required,
        };
      }

      const validPermissionIds = validPermissions.map((p) => p.id);

      // Fetch existing permissions for the role
      const existingRolePermissions = await rolePermissionRepo.find({
        where: { role: { id: roleId } },
        relations: ["permission"],
      });

      const existingPermissionIds = existingRolePermissions.map(
        (rp) => rp.permission.id
      );

      const toRemove = existingPermissionIds.filter(
        (id) => !validPermissionIds.includes(id)
      );

      const toAdd = validPermissionIds.filter(
        (id) => !existingPermissionIds.includes(id)
      );

      if (toRemove.length > 0) {
        await rolePermissionRepo
          .createQueryBuilder()
          .delete()
          .where("roleId = :roleId", { roleId })
          .andWhere("permissionId IN (:...toRemove)", { toRemove })
          .execute();
      }

      if (toAdd.length > 0) {
        const newRolePermissions = toAdd.map((pid) => {
          const rolePermission = new RolePermissionModel();
          rolePermission.role = role;
          rolePermission.permission = validPermissions.find(
            (p) => p.id === pid
          )!;
          rolePermission.updatedTs = new Date();
          return rolePermission;
        });
        await rolePermissionRepo.save(newRolePermissions);
      }

      await queryRunner.commitTransaction();

      // Update Redis cache for all employees with this role
      try {
        // Create cache instance
        const cache = new Cache();

        // Get permission slugs for the updated role
        const permissionSlugs = await permissionRepo
          .createQueryBuilder("permission")
          .innerJoin(
            "role_permissions_mapping",
            "rp",
            "rp.permission_id = permission.id"
          )
          .where("rp.role_id = :roleId", { roleId: role.id })
          .select("permission.slug", "permissionSlug")
          .getRawMany();

        // Extract permission slug array
        const slugs = permissionSlugs.map((p) => p.permissionSlug);

        // Update Redis cache for the role
        const permissionsKey = REDIS_KEYS.ROLE_PERMISSIONS.replace(
          "{roleId}",
          String(role.id)
        );
        const statusKey = REDIS_KEYS.ROLE_PERMISSIONS_UPDATE_STATUS.replace(
          "{roleId}",
          String(role.id)
        );

        // Store new permission slugs in Redis
        await cache.set(
          permissionsKey,
          JSON.stringify(slugs),
          REDIS_EXPIRY.DEFAULT
        );

        // Set the permission_update_status flag to true
        await cache.set(
          statusKey,
          "true",
          REDIS_EXPIRY.PERMISSIONS_UPDATE_STATUS
        );
      } catch (cacheError) {
        // Just log the cache error but don't fail the operation
        Sentry.captureException(cacheError);
      }

      // Get just the updated role with permission count
      const updatedRoleData = await roleRepo
        .createQueryBuilder("role")
        .leftJoin(
          RolePermissionModel,
          "rolePermission",
          "rolePermission.role_id = role.id"
        )
        .where("role.id = :roleId", { roleId })
        .select([
          "role.id as id",
          "role.name as name",
          "role.isDefaultRole as isDefaultRole",
          "COUNT(DISTINCT rolePermission.id) as permission_count",
          "COALESCE(MAX(rolePermission.updated_ts), role.updatedTs) as updated_ts",
        ])
        .groupBy("role.id")
        .getRawOne();

      return {
        data: updatedRoleData,
        success: true,
        message: ACCESS_MANAGEMENT_MSG.role_permissions_updated,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.log("error", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: ACCESS_MANAGEMENT_MSG.update_failed,
      };
    } finally {
      await queryRunner.release();
    }
  };

  /**
   * Get role permissions by role ID
   * @param roleId - ID of the role to get permissions for
   */
  static getRolePermissionsByRoleId = async (roleId: number, orgId: number) => {
    try {
      // Check if role exists
      const roleRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(RoleModel);
      const role = await roleRepo.findOne({
        where: {
          id: roleId,
          isActive: true,
          organizationId: orgId,
        },
      });

      if (!role) {
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.role_not_found,
        };
      }
      if (role.isDefaultRole) {
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.default_role_update_not_allowed,
        };
      }

      // Get all permissions with their selected status in a single query
      const dataConnection = await DbConnection.getS9InnerviewDataSource();
      const permissions = await dataConnection
        .createQueryBuilder()
        .select("p.id", "id")
        .addSelect("p.name", "name")
        .addSelect("p.description", "description")
        .addSelect(
          "CASE WHEN rp.id IS NOT NULL THEN TRUE ELSE FALSE END",
          "selected"
        )
        .from(PermissionModel, "p")
        .leftJoin(
          RolePermissionModel,
          "rp",
          "rp.permission_id = p.id AND rp.role_id = :roleId",
          { roleId }
        )
        .getRawMany()
        .then((results) =>
          results.map((item) => ({
            ...item,
            selected:
              item.selected === 1 ||
              item.selected === true ||
              item.selected === "1",
          }))
        );

      return {
        success: true,
        message: ACCESS_MANAGEMENT_MSG.role_permissions_fetch,
        data: {
          role_id: role.id,
          role_name: role.name,
          permissions,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
      };
    }
  };

  /**
   * Get user permissions by user ID
   * First tries to fetch from Redis using role ID, if not found, gets from DB and stores in Redis
   *
   * @param userId - The user ID
   * @returns Object containing success status, message and permission slugs
   */
  static getUserPermissions = async (roleId: number, forceFetch = false) => {
    try {
      // Initialize Redis cache
      const cache = new Cache();
      const permissionsKey = REDIS_KEYS.ROLE_PERMISSIONS.replace(
        "{roleId}",
        String(roleId)
      );
      const statusKey = REDIS_KEYS.ROLE_PERMISSIONS_UPDATE_STATUS.replace(
        "{roleId}",
        String(roleId)
      );

      // Check permission_update_status flag
      const updateStatus = await cache.get(statusKey);

      // Try to get permissions from Redis first if updateStatus is true
      if (updateStatus === "true" || forceFetch) {
        const permissionSlugs = await cache.get(permissionsKey);
        let rolePermissions = JSON.parse(permissionSlugs);

        // get data from database based in role if permissions are not found in cache ans save in cache
        if (!rolePermissions || rolePermissions.length === 0) {
          const permissionRepo =
            await DbConnection.getS9InnerViewDatabaseRepository(
              PermissionModel
            );

          const permissions = await permissionRepo
            .createQueryBuilder("permission")
            .innerJoin(
              "role_permissions_mapping",
              "rp",
              "rp.permission_id = permission.id"
            )
            .where("rp.role_id = :roleId", { roleId })
            .select("permission.slug", "permissionSlug")
            .getRawMany();

          // Extract permission slug array
          rolePermissions = permissions.map((rp) => rp.permission.slug);
          // Save permissions in Redis cache
          await cache.set(
            permissionsKey,
            JSON.stringify(rolePermissions),
            REDIS_EXPIRY.DEFAULT
          );
        }

        if (updateStatus === "true") {
          // Set permission_update_status to false after retrieving from Redis
          await cache.set(
            statusKey,
            "false",
            REDIS_EXPIRY.PERMISSIONS_UPDATE_STATUS
          );
        }

        if (rolePermissions.length === 0) {
          return {
            success: true,
            message: API_RESPONSE_MSG.success,
            data: { rolePermissions },
          };
        }

        return {
          success: true,
          message: API_RESPONSE_MSG.success,
          data: { rolePermissions },
        };
      }

      // When updateStatus is false, return a specific response without querying the database
      return {
        success: false,
        message: ACCESS_MANAGEMENT_MSG.no_update_in_permissions,
        data: { rolePermissions: [] },
      };
    } catch (error) {
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
        data: { rolePermissions: [] },
      };
    }
  };

  static addRoleDepartmentAndPermissionForAdmin = async (
    data: {
      organizationId: number;
      userId: number;
    },
    assignedBy: number
  ) => {
    try {
      const { organizationId, userId } = data;

      // Add default department
      const departmentRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(DepartmentModel);

      const departmentData = new DepartmentModel();
      departmentData.name = DEFAULT_ORG_FIELDS.DEPARTMENT;
      departmentData.organizationId = organizationId;
      departmentData.isDefaultDepartment = true;
      departmentData.createdTs = new Date();
      departmentData.updatedTs = new Date();
      await departmentRepo.save(departmentData);

      // Add default role
      const roleRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(RoleModel);

      const roleData = new RoleModel();
      roleData.name = DEFAULT_ORG_FIELDS.ROLE;
      roleData.organizationId = organizationId;
      roleData.isDefaultRole = true;
      roleData.createdTs = new Date();
      roleData.updatedTs = new Date();
      await roleRepo.save(roleData);

      const employeeRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(Employee);

      const employeeData = new Employee();
      employeeData.userId = userId;
      employeeData.organizationId = organizationId;
      employeeData.departmentId = departmentData?.id;
      employeeData.roleId = roleData?.id;
      employeeData.assignedBy = assignedBy;
      employeeData.interviewOrder = 1;
      employeeData.isAdmin = true;
      employeeData.createdTs = new Date();
      employeeData.updatedTs = new Date();
      await employeeRepo.save(employeeData);

      // Add default role permissions
      const permissionRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(PermissionModel);
      // Get all permissions
      const permissions = await permissionRepo.find();

      const rolePermissionDataArray = permissions.map((permission) => {
        const rolePermissionData = new RolePermissionModel();
        rolePermissionData.roleId = roleData?.id;
        rolePermissionData.permissionId = Number(permission.id);
        rolePermissionData.createdTs = new Date();
        rolePermissionData.updatedTs = new Date();
        return rolePermissionData;
      });

      const rolePermissionRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(
          RolePermissionModel
        );

      // bulk insertion
      await rolePermissionRepo.save(rolePermissionDataArray);

      console.log(
        "Default role, department and permissions added successfully"
      );

      // Create a free plan subscription for the new organization
      await SubscriptionServices.createFreeSubscription(organizationId);

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
      };
    }
  };
}

export default AccessManagementServices;
