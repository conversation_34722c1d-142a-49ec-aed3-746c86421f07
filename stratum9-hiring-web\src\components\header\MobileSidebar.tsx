import React, { useState } from "react";
import NavSettingsIcon from "../svgComponents/NavSettingsIcon";
import NavJobsIcon from "../svgComponents/NavJobsIcon";
import NavCalendarIcon from "../svgComponents/NavCalendarIcon";
import NavCandidatesIcon from "../svgComponents/NavCandidatesIcon";
import NavHomeIcon from "../svgComponents/NavHomeIcon";
import { useRouter } from "next/navigation";
import ROUTES from "@/constants/routes";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import NotificationIcon from "../svgComponents/Notification";
import LogoutIcon from "../svgComponents/LogoutIcon";
import { useSelector } from "react-redux";
import { selectProfileData } from "@/redux/slices/authSlice";
import { IUserData } from "@/interfaces/authInterfaces";
import Image from "next/image";
import User from "../../../public/assets/images/user.png";
import downArrow from "../../../public/assets/images/down-arrow.svg";
import ProfileIcon from "../svgComponents/ProfileIcon";
import { useTranslations } from "next-intl";

const MobileSidebar = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
  const navigate = useRouter();
  const [subLink, setSubLink] = useState(false);
  const userProfile: IUserData | null = useSelector(selectProfileData);
  const t = useTranslations("header");

  return (
    <>
      <div className={`sidebar ${isOpen ? "open" : ""}`}>
        <div className="sidebar-header">
          <Image src={userProfile?.image || User} alt="Profile" className="sidebar-profile" width={100} height={100} />
          <h5 id="dropdown-user-name">{`${userProfile?.first_name}`}</h5>

          <Button onClick={onClose} className="clear-btn p-0">
            <ModalCloseIcon />
          </Button>
        </div>
        <div className="sidebar-menu">
          <li
            onClick={() => {
              navigate.push(ROUTES.DASHBOARD);
              onClose();
            }}
          >
            <NavHomeIcon /> Home
          </li>
          <li
            onClick={() => {
              navigate.push(ROUTES.SCREEN_RESUME.CANDIDATES);
              onClose();
            }}
          >
            <NavCandidatesIcon /> Candidates
          </li>
          <li
            onClick={() => {
              navigate.push(ROUTES.INTERVIEW.CALENDAR);
              onClose();
            }}
          >
            <NavCalendarIcon /> Calendar
          </li>
          <li
            onClick={() => {
              navigate.push(ROUTES.JOBS.ACTIVE_JOBS);
              onClose();
            }}
          >
            <NavJobsIcon /> Jobs
          </li>
          <li className="sub-menu-bar" onClick={() => setSubLink(!subLink)}>
            <div className="sub-menu-list">
              <NavSettingsIcon />{" "}
              <span>
                Settings{" "}
                <Image src={downArrow} alt="downArrow" style={{ rotate: `${subLink ? "180deg" : "0deg"}`, width: "13px", marginLeft: "5px" }} />
              </span>
            </div>
            {subLink && (
              <ul className="sidebar-menu sidebar-sub-menu">
                <li
                  onClick={() => {
                    navigate.push(ROUTES.ROLE_EMPLOYEES.ROLES_PERMISSIONS);
                  }}
                >
                  Roles and Permissions
                </li>
                <li
                  onClick={() => {
                    navigate.push(ROUTES.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT);
                  }}
                >
                  Employee Management
                </li>
              </ul>
            )}
          </li>
          <li>
            <NotificationIcon />
            Notifications
          </li>
          <li
            onClick={() => {
              navigate.push(ROUTES.DASHBOARD);
              onClose();
            }}
          >
            <ProfileIcon /> {t("my_profile")}
          </li>
          <li>
            <LogoutIcon className="strokeSvg" />
            Logout
          </li>
        </div>
      </div>

      {isOpen && <div className="overlay" onClick={onClose}></div>}
    </>
  );
};

export default MobileSidebar;
