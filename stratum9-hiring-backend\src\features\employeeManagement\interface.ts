export interface IDepartmentData {
  name: string;
  organizationId?: number;
}

export interface IUpdateDepartmentData {
  name: string;
  organizationId?: number;
}

export interface IEmployeeData {
  name: string;
  email: string;
  departmentId: number;
  roleId: number;
  interviewOrder: number;
}

export interface IEmployeesData {
  employees: IEmployeeData[];
}

export interface IHireEmployeeData {
  firstName: string;
  lastName: string;
  email: string;
  departmentId: number;
  roleId: number;
}

export interface IAddEmployee {
  employees: IHireEmployeeData[];
}

export interface IEmployeeRoleUpdate {
  roleId: number;
  organizationId: number;
}

export interface IEmployeeInterviewOrderUpdate {
  newInterviewOrder: number;
  organizationId: number;
  employeeId: number;
  departmentId: number;
}

export interface IEmployeeQuery {
  departmentId: number;
  organizationId: number;
  search?: string;
  limit: number;
  offset: number;
}
