/**
 * Interface for User data
 */
export interface IUser {
  id: string;
  name: string;
  email: string;
  organization_id: string;
}

/**
 * Interface for Organization Subscription data
 */
export interface IOrganizationSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  status: string;
  expiry_date: Date;
  subscription_id: string;
  payment_method_id?: string;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface for Stripe webhook event handling
 */
/**
 * Stripe object types for webhook events
 */
export interface IStripeObject {
  id: string;
  object: string;
  [key: string]: unknown;
}

export interface IStripeWebhookEvent {
  id: string;
  type: string;
  data: {
    object: IStripeObject;
  };
  object: string;
}

/**
 * Interface for payment intent data
 */
export interface IPaymentIntent {
  id: string;
  amount: number;
  currency: string;
  customer: string;
  status: string;
  metadata: {
    subscription_id?: string;
    plan_id?: string;
    user_id?: string;
  };
}

/**
 * Interface for invoice data
 */
/**
 * Interface for Stripe payment intent
 */
export interface IStripePaymentIntent {
  id: string;
  object: string;
  amount: number;
  status: string;
  client_secret?: string;
  [key: string]: unknown;
}

/**
 * Interface for Stripe invoice parent
 */
export interface IStripeInvoiceParent {
  id: string;
  object: string;
  description?: string;
  subscription_details: {
    subscription: string;
    metadata: {
      orgId: string;
      planId: string;
      pricingId: string;
      pricingType: string;
    };
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

export interface IInvoice {
  parent: IStripeInvoiceParent;
  amount_paid: number;
  payment_intent: IStripePaymentIntent;
  id: string;
  subscription: string;
  customer: string;
  status: string;
  hosted_invoice_url?: string;
  lines: {
    data: Array<{
      plan?: {
        id: string;
        interval: string;
      };
    }>;
  };
  metadata: {
    orgId?: string;
    planId?: string;
    pricingId?: string;
    pricingType?: string;
  };
  // Unix timestamps from Stripe
  created: number;
  period_start: number;
  period_end: number;
  due_date?: number;
}

/**
 * Interface for subscription data
 */
/**
 * Interface for subscription cancellation details
 */
export interface ICancellationDetails {
  reason?: string;
  feedback?: string;
  comment?: string;
  [key: string]: unknown;
}

export interface ISubscription {
  cancellation_details: ICancellationDetails;
  cancellation_reason: string;
  id: string;
  customer: string;
  status: string;
  cancel_at_period_end: boolean;
  billing_cycle_anchor?: number;
  items: {
    data: Array<{
      plan: {
        id: string;
        interval: string;
      };
      current_period_end?: number;
      current_period_start?: number;
    }>;
  };
  metadata: {
    orgId?: string;
    planId?: string;
    pricingId?: string;
    pricingType?: string;
    userId?: string;
  };
  latest_invoice?: {
    payment_intent?: {
      id: string;
    };
  };
  // Unix timestamps from Stripe
  current_period_start: number;
  current_period_end: number;
  start_date: number;
  canceled_at?: number;
}

/**
 * Interface for pagination metadata
 */
export interface IPagination {
  total: number;
  offset: number;
  limit: number;
  hasMore: boolean;
}

/**
 * Interface for formatted transaction data
 */
export interface ITransactionData {
  id: number;
  payment_status: string;
  amount: number;
  transaction_type: string;
  transaction_method: string;
  transaction_date: Date;
  invoice_id: string;
  invoice_url?: string;
}

/**
 * Interface for paginated transactions data structure
 */
export interface IPaginatedTransactionsData {
  transactions: ITransactionData[];
  pagination: IPagination;
}

/**
 * Interface for paginated transactions response
 */
export interface IPaginatedTransactionsResponse {
  success: boolean;
  message: string;
  data: IPaginatedTransactionsData;
}
