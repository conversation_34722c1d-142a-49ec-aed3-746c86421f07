import endpoint from "@/constants/endpoint";
import { http } from "@/utils/http";

import { ApiResponse, IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import { PlanData, CancelSubscriptionRequest, TransactionResponse, BuySubscriptionResponse } from "@/interfaces/subscriptionInterfaces";
import { ICurrentPlan } from "@/redux/slices/authSlice";

/**
 * Centralized subscription service that handles all subscription-related API calls
 */
export const subscriptionService = {
  /**
   * Get all available subscription plans
   */
  getAllPlans: (): Promise<IApiResponseCommonInterface<PlanData[]>> => {
    return http.get(endpoint.subscription.GET_ALL_PLANS);
  },

  /**
   * Get current subscription with detailed information
   */
  getCurrentSubscription: (): Promise<IApiResponseCommonInterface<ICurrentPlan>> => {
    return http.get(endpoint.subscription.GET_CURRENT_SUBSCRIPTION);
  },

  /**
   * Cancel subscription plan
   */
  cancelPlan: async (data: CancelSubscriptionRequest): Promise<ApiResponse> => {
    return http.post(endpoint.subscription.CANCEL_SUBSCRIPTION, data);
  },

  /**
   * Fetch subscription transactions history from the API
   * @param data Object with pagination parameters
   * @returns Promise resolving to TransactionResponse with pagination data
   */
  getTransactions: (data?: { limit?: number; offset?: number }): Promise<IApiResponseCommonInterface<TransactionResponse>> => {
    return http.get(endpoint.subscription.GET_TRANSACTIONS, { params: data });
  },

  /**
   * Buy subscription - simplified API call that handles everything in one request
   * @param data Object with planId and pricingId
   * @returns Promise resolving to checkout URL and session details
   */
  buySubscription: (data: { planId: number; pricingId: number }): Promise<ApiResponse<BuySubscriptionResponse>> => {
    return http.post(endpoint.subscription.BUY_SUBSCRIPTION, data);
  },
};
