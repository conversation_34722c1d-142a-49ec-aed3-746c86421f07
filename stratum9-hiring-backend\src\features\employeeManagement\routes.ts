import express from "express";
import HandleErrors from "../../middleware/handleError";
import {
  getDepartments,
  addDepartment,
  deleteDepartment,
  getEmployeesByDepartment,
  addEmployees,
  // deleteEmployee,
  updateDepartment,
  updateEmployeeRole,
  updateEmployeeInterviewOrder,
  updateEmployeeStatus,
  getEmployeesByOrganizationId,
} from "./controller";
import {
  schemaValidation,
  queryValidation,
  paramsValidation,
} from "../../middleware/validateSchema";
import {
  addDepartmentValidation,
  addEmployeeValidation,
  updateEmployeeRoleValidation,
  updateDepartmentValidation,
  getDepartmentQueryValidation,
  departmentIdParamValidation,
  employeeIdParamValidation,
  getdepartmentEmployeeQueryValidation,
  updateEmployeeInterviewOrderValidation,
  updateEmployeeStatusValidation,
  getEmployeeByOrgIdQueryValidation,
} from "./validation";
import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";
import { authorizedForCreateNewDepartment } from "../../middleware/isAuthorized";
import adminAuth from "../../middleware/adminAuth";
// import adminAuth from "../../middleware/adminAuth";

const employeeManagementRoutes = express.Router();

/**
 * @swagger
 * /employee-management/departments:
 *   get:
 *     summary: Get All Departments
 *     tags:
 *       - Employee Management Routes
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: departments_fetch
 *                  data:
 *                    type: array
 *                    items:
 *                      type: object
 *                      properties:
 *                        id:
 *                          type: number
 *                        name:
 *                          type: string
 *                        isActive:
 *                          type: boolean
 *                        createdTs:
 *                          type: string
 *                          format: date-time
 *                        updatedTs:
 *                          type: string
 *                          format: date-time
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
employeeManagementRoutes.get(
  ROUTES.DEPARTMENTS,
  auth,
  queryValidation(getDepartmentQueryValidation),
  authorizedForCreateNewDepartment,
  HandleErrors(getDepartments)
);

/**
 * @swagger
 * /employee-management/department:
 *   post:
 *     summary: Add New Department
 *     tags:
 *       - Employee Management Routes
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: department_added
 *                  data:
 *                    type: object
 *                    properties:
 *                      id:
 *                        type: number
 *                      name:
 *                        type: string
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
employeeManagementRoutes.post(
  ROUTES.ADD_DEPARTMENT,
  auth,
  authorizedForCreateNewDepartment,
  schemaValidation(addDepartmentValidation),
  HandleErrors(addDepartment)
);

/**
 * @swagger
 * /employee-management/delete-department/{departmentId}:
 *   delete:
 *     summary: Delete Department
 *     tags:
 *       - Employee Management Routes
 *     parameters:
 *       - in: path
 *         name: departmentId
 *         required: true
 *         description: ID of the department to delete
 *         schema:
 *           type: number
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: department_deleted
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */

/**
 * Route to handle department deletion
 * Matches the frontend API call: deleteDepartment(departmentId, organizationId)
 * URL format: /delete-department/:departmentId with organizationId as query param
 */
employeeManagementRoutes.delete(
  ROUTES.DELETE_DEPARTMENT,
  auth,
  paramsValidation(departmentIdParamValidation),
  authorizedForCreateNewDepartment,
  HandleErrors(deleteDepartment)
);

/**
 * @swagger
 * /employee-management/update-department/{departmentId}:
 *   put:
 *     summary: Update Department
 *     tags:
 *       - Employee Management Routes
 *     parameters:
 *       - in: path
 *         name: departmentId
 *         required: true
 *         description: ID of the department to update
 *         schema:
 *           type: number
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               organizationId:
 *                 type: number
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: department_updated
 *                  data:
 *                    type: object
 *                    properties:
 *                      id:
 *                        type: number
 *                      name:
 *                        type: string
 *                      organizationId:
 *                        type: number
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */

/**
 * Route to handle department update
 * Matches the frontend API call: updateDepartment(departmentId, departmentData)
 * URL format: /update-department/:departmentId with name and organizationId in request body
 */
employeeManagementRoutes.put(
  ROUTES.DEPARTMENT_WITH_ID,
  auth,
  authorizedForCreateNewDepartment,
  schemaValidation(updateDepartmentValidation),
  paramsValidation(departmentIdParamValidation),
  HandleErrors(updateDepartment)
);

/**
 * @swagger
 * /employee-management/employees:
 *   get:
 *     summary: Get Employees by Department with Search and Pagination
 *     tags:
 *       - Employee Management Routes
 *     parameters:
 *       - in: query
 *         name: departmentId
 *         required: true
 *         description: ID of the department to get employees from
 *         schema:
 *           type: number
 *       - in: query
 *         name: search
 *         required: false
 *         description: Search string to filter employees by name, email, or role
 *         schema:
 *           type: string
 *       - in: query
 *         name: page
 *         required: false
 *         description: Page number for pagination
 *         schema:
 *           type: number
 *           default: 1
 *       - in: query
 *         name: limit
 *         required: false
 *         description: Number of items per page
 *         schema:
 *           type: number
 *           default: 10
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: employees_fetch
 *                  data:
 *                    type: object
 *                    properties:
 *                      employees:
 *                        type: array
 *                        items:
 *                          type: object
 *                          properties:
 *                            id:
 *                              type: number
 *                            name:
 *                              type: string
 *                            email:
 *                              type: string
 *                            interviewOrder:
 *                              type: number
 *                            department:
 *                              type: object
 *                              properties:
 *                                id:
 *                                  type: number
 *                                name:
 *                                  type: string
 *                            role:
 *                              type: object
 *                              properties:
 *                                id:
 *                                  type: number
 *                                name:
 *                                  type: string
 *                      pagination:
 *                        type: object
 *                        properties:
 *                          total:
 *                            type: number
 *                          page:
 *                            type: number
 *                          limit:
 *                            type: number
 *                          totalPages:
 *                            type: number
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
employeeManagementRoutes.get(
  ROUTES.EMPLOYEES,
  auth,
  queryValidation(getdepartmentEmployeeQueryValidation),
  HandleErrors(getEmployeesByDepartment)
);

/**
 * @swagger
 * /employee-management/employees:
 *   post:
 *     summary: Add Multiple Employees
 *     tags:
 *       - Employee Management Routes
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               employees:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     email:
 *                       type: string
 *                       format: email
 *                     departmentId:
 *                       type: number
 *                     roleId:
 *                       type: number
 *                     interviewOrder:
 *                       type: number
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: employees_added
 *                  data:
 *                    type: object
 *                    properties:
 *                      addedCount:
 *                        type: number
 *                      errors:
 *                        type: array
 *                        items:
 *                          type: string
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
employeeManagementRoutes.post(
  ROUTES.ADD_EMPLOYEES,
  auth,
  schemaValidation(addEmployeeValidation),
  HandleErrors(addEmployees)
);

/**
 * @swagger
 * /employee-management/employee/{employeeId}/role:
 *   put:
 *     summary: Update Employee Role
 *     tags:
 *       - Employee Management Routes
 *     parameters:
 *       - in: path
 *         name: employeeId
 *         required: true
 *         description: ID of the employee to update
 *         schema:
 *           type: number
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               roleId:
 *                 type: number
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: employee_role_updated
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
employeeManagementRoutes.put(
  ROUTES.UPDATE_EMPLOYEE_ROLE,
  auth,
  schemaValidation(updateEmployeeRoleValidation),
  paramsValidation(employeeIdParamValidation),
  HandleErrors(updateEmployeeRole)
);

/**
 * @swagger
 * /employee-management/employee/{employeeId}:
 *   delete:
 *     summary: Delete Employee
 *     tags:
 *       - Employee Management Routes
 *     parameters:
 *       - in: path
 *         name: employeeId
 *         required: true
 *         description: ID of the employee to delete
 *         schema:
 *           type: number
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: employee_deleted
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
// employeeManagementRoutes.delete(
//   ROUTES.DELETE_EMPLOYEE,
//   auth,
//   paramsValidation(employeeIdParamValidation),
//   HandleErrors(deleteEmployee)
// );

/**
 * @swagger
 * /employee-management/employee/{employeeId}/interview-order:
 *   put:
 *     summary: Update Employee Interview Order
 *     tags:
 *       - Employee Management Routes
 *     parameters:
 *       - in: path
 *         name: employeeId
 *         required: true
 *         description: ID of the employee to update
 *         schema:
 *           type: number
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               newInterviewOrder:
 *                 type: number
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: employee_interview_order_updated
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
employeeManagementRoutes.put(
  ROUTES.UPDATE_EMPLOYEE_INTERVIEW_ORDER,
  auth,
  schemaValidation(updateEmployeeInterviewOrderValidation),
  paramsValidation(employeeIdParamValidation),
  HandleErrors(updateEmployeeInterviewOrder)
);

/**
 * @swagger
 * /employee-management/employee/change-status/{employeeId}:
 *   put:
 *     summary: Update Employee Status
 *     tags:
 *       - Employee Management Routes
 *     parameters:
 *       - in: path
 *         name: employeeId
 *         required: true
 *         description: ID of the employee to update
 *         schema:
 *           type: number
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: boolean
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: employee_status_updated
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
employeeManagementRoutes.put(
  ROUTES.UPDATE_EMPLOYEE_STATUS,
  auth,
  schemaValidation(updateEmployeeStatusValidation),
  paramsValidation(employeeIdParamValidation),
  HandleErrors(updateEmployeeStatus)
);

/**
 * Get employees by organization ID
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
employeeManagementRoutes.get(
  ROUTES.GET_EMPLOYEES_BY_ORGANIZATION_ID,
  adminAuth,
  queryValidation(getEmployeeByOrgIdQueryValidation),
  HandleErrors(getEmployeesByOrganizationId)
);

export default employeeManagementRoutes;
