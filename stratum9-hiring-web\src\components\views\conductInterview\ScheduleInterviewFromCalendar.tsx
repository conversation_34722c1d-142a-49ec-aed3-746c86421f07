/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { DateSelectArg, DatesSetArg, EventClickArg } from "@fullcalendar/core";
import { useTranslations } from "next-intl";
import { debounce } from "lodash";
import { yupResolver } from "@hookform/resolvers/yup";

import { formatTimeForInput, toastMessageError, toastMessageSuccess } from "@/utils/helper";
import CalenderEventModal from "@/components/commonModals/CalendarEventModal";
import style from "../../../styles/conductInterview.module.scss";
import { getInterviewers, getJobList, getMyInterviews, updateOrScheduleInterview } from "@/services/interviewServices";
import { scheduleInterviewValidation } from "@/validations/interviewValidations";
import { IGetInterviewersResponse, IGetInterviewsResponse, IGetJobListResponse, ScheduleInterviewFormValues } from "@/interfaces/interviewInterfaces";
import InterviewDetailModal from "@/components/commonModals/InterviewDetailModal";
import CommonCalendar from "@/components/commonComponent/CommonCalendar";
import { PERMISSION, ScheduleInterviewFormSubmissionType } from "@/constants/commonConstants";
import { AuthState } from "@/redux/slices/authSlice";
import { useSelector } from "react-redux";
import toast from "react-hot-toast";

export const defaultValues = {
  eventTitle: "",
  date: "",
  startTime: "",
  endTime: "",
  description: "",
  interviewer: -1,
  jobId: "",
  jobTitle: -1,
  interviewType: "",
  candidate: -1,
};

const ScheduleInterviewFromCalendar = () => {
  const t = useTranslations();
  const initialFetchCompleted = useRef(false);

  const [interviews, setInterviews] = useState<IGetInterviewsResponse[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [interviewers, setInterviewers] = useState<IGetInterviewersResponse[]>([]);
  const [monthYear, setMonthYear] = useState<string>(`${String(new Date().getMonth() + 1).padStart(2, "0")}-${new Date().getFullYear()}`);
  const [loader, setLoader] = useState(false);
  const [jobLoader, setJobLoader] = useState(false);
  const [fileUrls, setFileUrls] = useState<string[]>([]);
  const [interviewInfo, setInterviewInfo] = useState<IGetInterviewsResponse | null>(null);
  const [jobs, setJobs] = useState<IGetJobListResponse[]>([]);
  const [formType, setFormType] = useState<(typeof ScheduleInterviewFormSubmissionType)[keyof typeof ScheduleInterviewFormSubmissionType]>(
    ScheduleInterviewFormSubmissionType.SCHEDULE
  );

  const currentFileArrayLengthRef = useRef(fileUrls ? fileUrls?.length : 0);

  const userPermissions = useSelector((state: { auth: AuthState }) => state.auth.permissions || []) as unknown as string[];

  const hasScheduleInterviewPermission = userPermissions.includes(PERMISSION.SCHEDULE_CONDUCT_INTERVIEWS);

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    setError,
    formState: { errors },
    getValues,
  } = useForm({
    resolver: yupResolver(scheduleInterviewValidation(t)),
  });

  console.log("fileUrls==", fileUrls);
  console.log("currentFileArrayLengthRef.current==", currentFileArrayLengthRef.current);

  const getAllInterviews = async (monthYear: string) => {
    try {
      const result = await getMyInterviews(monthYear);

      console.log("result", result);
      if (result?.data?.success) {
        const events = result?.data?.data;
        setInterviews(events);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onSubmit = async (data: ScheduleInterviewFormValues) => {
    setLoading(true);
    try {
      const { eventTitle, date, startTime, endTime, description, jobTitle, candidate, interviewer, interviewType } = data;
      console.log("inside onsubmit");

      const startDateTime = new Date(`${date}T${startTime}`);
      const endDateTime = new Date(`${date}T${endTime}`);

      // Convert to timestamp (milliseconds since epoch)
      const startTimestamp = startDateTime.getTime();
      const endTimestamp = endDateTime.getTime();
      const scheduleAtTimestamp = new Date().getTime();

      const updatePayload = {
        title: eventTitle,
        interviewerId: +interviewer,
        scheduleAt: scheduleAtTimestamp,
        startTime: startTimestamp,
        endTime: endTimestamp,
        description: description ?? "",
        roundType: interviewType,
        jobApplicationId: +candidate,
        interviewId: +interviewInfo?.id!,
        fileUrlArray: JSON.stringify(fileUrls),
      };

      const payload = {
        ...updatePayload,
        interviewId: undefined,
        jobId: +jobTitle,
      };

      console.log("payload======>>>>", payload);

      const result = await updateOrScheduleInterview(formType === ScheduleInterviewFormSubmissionType.SCHEDULE ? payload : updatePayload);

      if (result?.data?.success) {
        toastMessageSuccess(t(result?.data?.message));
        setIsModalOpen(false);
        reset();
        setFileUrls([]);
        currentFileArrayLengthRef.current = 0;
        getAllInterviews(monthYear);
        setLoading(false);
      } else {
        setLoading(false);
        toastMessageError(t(result?.data?.message ?? "something_went_wrong"));
      }
    } catch (error) {
      console.log(error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  const handleDatesSet = (info: DatesSetArg) => {
    console.log("Dates set:", info);

    // Get start date and add 10 days to ensure we're in the current month view
    const startDate = new Date(info.start);
    const adjustedDate = new Date(startDate);
    adjustedDate.setDate(startDate.getDate() + 7);

    // Format as MM-YYYY
    const month = String(adjustedDate.getMonth() + 1).padStart(2, "0"); // +1 because months are 0-indexed
    const year = adjustedDate.getFullYear();
    const formattedDate = `${month}-${year}`;

    if (formattedDate !== monthYear || !initialFetchCompleted.current) {
      setMonthYear(formattedDate);
      setInterviews([]);
      getAllInterviews(formattedDate);

      initialFetchCompleted.current = true;
    }
  };

  const handleEventClick = (info: EventClickArg) => {
    console.log("Event clicked:", info.event);

    const interviewInfoObj = interviews.find((interview) => Number(interview.id) === Number(info.event.id)) ?? null;

    console.log("interviewInfoObj", interviewInfoObj);

    setInterviewInfo(interviewInfoObj);
    const filesUrls = interviewInfoObj ? JSON.parse(interviewInfoObj?.attachments)?.fileUrls : [];
    setFileUrls(filesUrls);
    setIsViewModalOpen(true);
  };

  const handleOnSelect = (info: DateSelectArg) => {
    console.log("Event selected:", info);
    if (!hasScheduleInterviewPermission) {
      toast.dismiss();
      console.log("inside if", !hasScheduleInterviewPermission);
      toastMessageError(t("you_dont_have_permission_to_schedule_interview"));
      return;
    }
    // Parse the selected date information
    const startDate = new Date(info.start);
    const endDate = new Date(info.end);

    // Format date as YYYY-MM-DD for date input
    const formattedDate = startDate.toLocaleDateString("en-CA");
    console.log("formattedDate", formattedDate);

    const startTime = formatTimeForInput(startDate);
    const endTime = formatTimeForInput(endDate);

    setValue("date", formattedDate);
    setValue("startTime", startTime);
    setValue("endTime", endTime);
    setIsModalOpen(true);
    // getInterviewersList("");
    getJobs("");
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setFormType(ScheduleInterviewFormSubmissionType.SCHEDULE);
    reset(defaultValues);
  };

  const watchedJobTitle = watch("jobTitle");

  const getInterviewersList = useCallback(
    async (searchString: string) => {
      setLoader(true);
      try {
        const response = await getInterviewers(searchString, watchedJobTitle?.toString());

        if (response?.data?.success) {
          setInterviewers(response?.data?.data);
        }
      } catch (error) {
        console.error("Error fetching interviewers:", error);
      } finally {
        setLoader(false);
      }
    },
    [watchedJobTitle]
  );

  useEffect(() => {
    getInterviewersList("");
  }, [watchedJobTitle]);

  // console.log("watch", watch("jobTitle"));

  const handleSearchInputChange = (event: string) => {
    const searchString = event.trim();
    console.log("searchString", searchString);
    getInterviewersList(searchString);
  };

  const debouncedHandleSearchInputChange = debounce(handleSearchInputChange, 1000);

  const getJobs = useCallback(async (searchString: string) => {
    setJobLoader(true);
    try {
      const response = await getJobList(searchString);

      if (response?.data?.success) {
        setJobs(response?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching interviewers:", error);
    } finally {
      setJobLoader(false);
    }
  }, []);

  const handleJobSearchInputChange = (event: string) => {
    const searchString = event.trim();
    console.log("searchString", searchString);
    getJobs(searchString);
  };

  const debouncedHandleJobSearchInputChange = debounce(handleJobSearchInputChange, 1000);

  const onHandleEdit = () => {
    console.log("inside edit");
    setIsViewModalOpen(false);
    setFormType(ScheduleInterviewFormSubmissionType.UPDATE);
    setIsModalOpen(true);

    const startDate = interviewInfo?.start ? new Date(interviewInfo.start) : new Date();
    const endDate = interviewInfo?.end ? new Date(interviewInfo.end) : new Date();

    const startTime = formatTimeForInput(startDate);
    const endTime = formatTimeForInput(endDate);

    reset({
      eventTitle: interviewInfo?.title,
      date: startDate.toLocaleDateString("en-CA"),
      startTime,
      endTime,
      description: interviewInfo?.description,
      interviewer: interviewInfo?.interviewerId,
      jobId: interviewInfo?.jobUniqueId,
      jobTitle: interviewInfo?.jobId,
      interviewType: interviewInfo?.roundType,
      candidate: interviewInfo?.jobApplicationId,
    });

    getInterviewersList("");
  };

  const onHandleViewModelClose = () => {
    setInterviewInfo(null);
    setFileUrls([]);
    setIsViewModalOpen(false);
    reset(defaultValues);
  };

  return (
    <div className={style.conduct_interview_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                {t("dashboard_")} - <span>{t("calendar")}</span>
              </h2>
            </div>
          </div>
        </div>
        <div className="inner-section">
          <CommonCalendar
            handleDatesSet={handleDatesSet}
            handleOnSelect={handleOnSelect}
            interviews={interviews}
            handleEventClick={handleEventClick}
          />
          {isModalOpen ? (
            <CalenderEventModal
              control={control}
              onClose={handleModalClose}
              handleSubmit={handleSubmit((data) => {
                console.log("data", data);
                return onSubmit(data as ScheduleInterviewFormValues);
              })}
              setFileUrls={setFileUrls}
              fileUrls={fileUrls}
              loading={loading}
              jobs={jobs}
              jobLoader={jobLoader}
              errors={errors}
              currentFileArrayLengthRef={currentFileArrayLengthRef}
              interviewers={interviewers}
              loader={loader}
              setValue={setValue}
              getValues={getValues}
              setError={setError}
              interviewInfo={interviewInfo}
              setJobs={setJobs}
              formType={formType}
              debouncedHandleSearchInputChange={debouncedHandleSearchInputChange}
              debouncedHandleJobSearchInputChange={debouncedHandleJobSearchInputChange}
            />
          ) : null}
          {isViewModalOpen ? (
            <InterviewDetailModal onEdit={onHandleEdit} onClose={onHandleViewModelClose} interviewInfo={interviewInfo} attachments={fileUrls} />
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default ScheduleInterviewFromCalendar;
