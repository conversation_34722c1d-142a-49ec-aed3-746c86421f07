import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

import { PERMISSION, PERMISSIONS_COOKIES_KEY } from "./constants/commonConstants";
import ROUTES, { BEFORE_LOGIN_ROUTES, UNRESTRICTED_ROUTES } from "./constants/routes";
// import { IUserData, Role } from "./interfaces/authInterfaces";

// Define session data structure with permissions
// type SessionData = {
//   data?: {
//     token?: string;
//     authData: { userData: IUserData; role: Role; permissions?: string[] };
//     [key: string]: unknown;
//   };
//   [key: string]: unknown;
// };

// Route permission mapping
const routePermissionMap: { [key: string]: string | string[] } = {
  // User role routes
  [ROUTES.ROLE_EMPLOYEES.ROLES_PERMISSIONS]: PERMISSION.CREATE_NEW_ROLE,

  // Archive route (contains both candidates and jobs tabs)
  [ROUTES.JOBS.ARCHIVE]: [PERMISSION.ARCHIVE_RESTORE_CANDIDATES, PERMISSION.ARCHIVE_RESTORE_JOB_POSTS],

  // Manual Resume Screening routes - protect all paths including dynamic routes
  [ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD]: PERMISSION.MANUAL_RESUME_SCREENING,
  [ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD + "/[...path]"]: PERMISSION.MANUAL_RESUME_SCREENING,

  // Department management - these routes are accessed from employee-management
  [ROUTES.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT]: PERMISSION.CREATE_NEW_DEPARTMENT,

  // Job routes - require job post creation/editing permission
  [ROUTES.JOBS.JOB_EDITOR]: PERMISSION.CREATE_OR_EDIT_JOB_POST,
  [ROUTES.JOBS.GENERATE_JOB]: PERMISSION.CREATE_OR_EDIT_JOB_POST,
  [ROUTES.JOBS.ACTIVE_JOBS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,
  [ROUTES.JOBS.CAREER_BASED_SKILLS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,
  [ROUTES.JOBS.ROLE_BASED_SKILLS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,
  [ROUTES.JOBS.CULTURE_BASED_SKILLS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,
  [ROUTES.JOBS.EDIT_SKILLS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,
  [ROUTES.JOBS.HIRING_TYPE]: PERMISSION.CREATE_OR_EDIT_JOB_POST,

  // Buy subscription routes
  [ROUTES.BUY_SUBSCRIPTION]: PERMISSION.MANAGE_SUBSCRIPTIONS,

  // Add other protected routes and their required permissions here
};

// Helper function to normalize route path
const normalizePath = (path: string): string => {
  // Remove trailing slash except for root path
  return path === "/" ? path : path.replace(/\/$/, "");
};

// Helper function to convert Next.js dynamic route pattern to regex pattern
const routeToRegex = (pattern: string): RegExp => {
  // Normalize and escape the pattern for regex
  const regexPattern = normalizePath(pattern)
    // Escape special regex characters except [ and ]
    .replace(/[.+?^${}()|\\\/]/g, "\\$&")
    // Replace [...param] catch-all routes
    .replace(/\[\.\.\.[^\]]+\]/g, ".*")
    // Replace [param] dynamic segments
    .replace(/\[[^\]]+\]/g, "[^/]+");

  return new RegExp(`^${regexPattern}$`);
};

// Type for route patterns map entries
type RoutePattern = [RegExp, string | string[]];

// Pre-compile regex patterns for dynamic routes
const dynamicRoutePatterns = new Map<RegExp, string | string[]>(
  Object.entries(routePermissionMap)
    .filter(([pattern]) => pattern.includes("["))
    .map(([pattern, permissions]): RoutePattern => [routeToRegex(pattern), permissions])
    // Sort patterns by specificity (more specific routes first)
    .sort((a, b) => {
      const segmentsA = a[0].toString().split("/").length;
      const segmentsB = b[0].toString().split("/").length;
      return segmentsB - segmentsA;
    })
);

// Convert routePermissionMap to Map for faster lookups
const staticRoutePermissions = new Map<string, string | string[]>(Object.entries(routePermissionMap).filter(([pattern]) => !pattern.includes("[")));

// Helper function to check if user has any of the required permissions
const hasAnyPermission = (userPermissions: string[], required: string | string[]): boolean => {
  return Array.isArray(required) ? required.some((perm) => userPermissions.includes(perm)) : userPermissions.includes(required);
};

// Helper function to check if a user has permission for a given route
const hasPermissionForRoute = (path: string, userPermissions: string[]): boolean => {
  console.log("userPermissions>>>>>>>", userPermissions);

  // Early return for empty permissions array
  if (!userPermissions.length) return false;

  // Check static routes first (faster lookup)
  const staticPermissions = staticRoutePermissions.get(path);

  if (staticPermissions) {
    return hasAnyPermission(userPermissions, staticPermissions);
  }

  // Check dynamic routes
  for (const [pattern, requiredPermissions] of dynamicRoutePatterns) {
    console.log(`>>>>>>>>>>>>>Checking dynamic route: ${pattern} for path: ${path}`);
    console.log(">>>>>>>>>>>>>requiredPermissions", requiredPermissions);

    if (pattern.test(path)) {
      return hasAnyPermission(userPermissions, requiredPermissions);
    }
  }

  // No match found in either static or dynamic routes - allow unrestricted access
  return true;
};

// Helper function to parse cookie data
const parseCookieData = <T>(cookieString: string | undefined | null, cookieName: string): T | null => {
  if (!cookieString) return null;
  const cookieValue = cookieString.split(";").find((c) => c.trim().startsWith(`${cookieName}=`));
  if (!cookieValue) return null;

  try {
    // Extract the value part (after '=') and decode it
    const encodedValue = cookieValue.split("=")[1];
    if (!encodedValue) return null;
    // First decode the URL encoded string, then parse as JSON
    return JSON.parse(decodeURIComponent(encodedValue)) as T;
  } catch (error) {
    console.error(`Error parsing ${cookieName} cookie:`, error);
    return null;
  }
};

export default async function middleware(req: NextRequest) {
  try {
    const session = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    // Define types for our Redux data structures
    type AuthData = {
      permissions?: string[];
      [key: string]: unknown;
    };
    const isAuthenticated = !!session?.data;
    const path = req.nextUrl.pathname;
    const isPublicRoute = BEFORE_LOGIN_ROUTES.includes(path);
    // Check if the current path is in the unrestricted routes list
    const isUnrestrictedRoute = UNRESTRICTED_ROUTES.includes(path);
    const response = NextResponse.next();

    if (isAuthenticated) {
      if (isPublicRoute) {
        return NextResponse.redirect(new URL(ROUTES.DASHBOARD, req.url));
      } else {
        if (isUnrestrictedRoute) {
          return response; // Allow unrestricted routes without permission check
        }
        const cookieHeader = req.headers.get("cookie");
        const authDataFromCookies = parseCookieData<AuthData>(cookieHeader, PERMISSIONS_COOKIES_KEY);
        const userPermissions = (authDataFromCookies?.length ? authDataFromCookies : []) as string[];

        // Check if user has permission for the route
        if (path === ROUTES.DASHBOARD || isUnrestrictedRoute || (userPermissions.length && hasPermissionForRoute(path, userPermissions))) {
          return response; // Allow access to the route if permissions are valid
        } else {
          // If authenticated user doesn't have permission for the route
          return NextResponse.redirect(new URL(ROUTES.DASHBOARD, req.url));
        }
      }
    } else if (!isPublicRoute) {
      return NextResponse.redirect(new URL(ROUTES.LOGIN, req.url));
    }
    return response;
  } catch (error) {
    console.error("Error in middleware:", error);
    return NextResponse.error();
  }
}

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico).*)"],
};
