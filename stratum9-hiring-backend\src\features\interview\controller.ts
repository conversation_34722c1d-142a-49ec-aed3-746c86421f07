import { Request, Response } from "express";
import InterviewService from "./services";
import {
  GetInterviewsData,
  IGetCandidateList,
  IGetInterviewSkillQuestions,
  IGetJobList,
  IUpdateInterviewSkillQuestion,
  IEndInterview,
} from "./interface";
import { DEFAULT_LIMIT } from "../../utils/constants";

export const conductInterviewStaticInformation = async (
  req: Request,
  res: Response
) => {
  try {
    const data = await InterviewService.conductInterviewStaticInformation();
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

export const getJobList = async (req: Request, res: Response) => {
  try {
    const body = {
      orgId: +req.orgId,
      searchString: req.query.searchString,
    };

    const data = await InterviewService.getJobList(body as IGetJobList);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

export const getCandidateList = async (req: Request, res: Response) => {
  try {
    const body = {
      orgId: +req.orgId,
      searchString: req.query.searchString,
      jobId: +req.query.jobId,
    };

    const data = await InterviewService.getCandidateList(
      body as IGetCandidateList
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get my interviews.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const getMyInterviews = async (req: Request, res: Response) => {
  try {
    const body = {
      ...(req.query as unknown as {
        monthYear: string;
      }),
      userId: req.userId,
      roleId: req.roleId,
    };

    const data = await InterviewService.getMyInterviews(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get interviews.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const getInterviews = async (req: Request, res: Response) => {
  try {
    const body = {
      jobId: req.query.jobId,
      applicationId: req.query.applicationId,
      monthYear: req.query.monthYear,
      interviewerId: +req.query.interviewerId,
    };

    const data = await InterviewService.getInterviews(
      body as GetInterviewsData
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get interviewers.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const getInterviewers = async (req: Request, res: Response) => {
  try {
    const data = await InterviewService.getInterviewers({
      orgId: req.orgId,
      jobId: +req.query.jobId,
      searchString: req.query.searchString as string,
    });
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Schedule interview.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const updateOrScheduleInterview = async (
  req: Request,
  res: Response
) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await InterviewService.updateOrScheduleInterview(
      body,
      req.userId,
      req.roleId
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get interview skill questions answers.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const getInterviewSkillQuestions = async (
  req: Request,
  res: Response
) => {
  try {
    const body = {
      jobApplicationId: +req.query.jobApplicationId,
      interviewId: +req.query.interviewId,
    };

    const data = await InterviewService.getInterviewSkillQuestions(
      body as IGetInterviewSkillQuestions
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Update interview skill question.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const updateInterviewSkillQuestion = async (
  req: Request,
  res: Response
) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await InterviewService.updateInterviewSkillQuestion(
      body as IUpdateInterviewSkillQuestion
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Add interview skill question.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const addInterviewSkillQuestion = async (
  req: Request,
  res: Response
) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await InterviewService.addInterviewSkillQuestion(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

// upcoming and past interviews

export const getUpcomingOrPastInterviews = async (
  req: Request,
  res: Response
) => {
  try {
    const { orgId } = req;

    // 🔹 Parse and validate query parameters
    const isPast = req.query.isPast === "true";
    const limit = Number(req.query.limit) || DEFAULT_LIMIT; // Max limit of 100
    const offset = Number(req.query.page) || 0;
    const searchStr = String(req.query.searchStr || "");

    // 🔹 Call the service with updated arguments
    const data = await InterviewService.getUpcomingOrPastInterviews(
      orgId,
      searchStr,
      isPast,
      offset,
      limit
    );

    res.status(200).json({ code: 200, ...data });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

export const updateInterviewAnswers = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await InterviewService.updateInterviewAnswers(
      body,
      req.userId
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * End interview and lock all evaluations.
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const endInterview = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await InterviewService.endInterview(body as IEndInterview);

    res.status(data.code || 200).json({
      ...data,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};
