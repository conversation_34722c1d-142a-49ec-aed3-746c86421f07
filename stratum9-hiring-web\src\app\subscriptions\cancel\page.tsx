"use client";
import React from "react";
import { useRouter } from "next/navigation";
import Button from "@/components/formElements/Button";
import ROUTES from "@/constants/routes";

const SubscriptionCancel = () => {
  const router = useRouter();

  const handleGoBack = () => {
    router.replace(ROUTES.BUY_SUBSCRIPTION);
  };

  const handleGoToDashboard = () => {
    router.replace(ROUTES.DASHBOARD);
  };

  return (
    <div className="subscription-successful-page">
      <div className="subscription-successful">
        <div className="success-icon">
          <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="40" cy="40" r="40" fill="#F44336" />
            <path
              d="M50.6668 29.3333L29.3335 50.6667M29.3335 29.3333L50.6668 50.6667"
              stroke="white"
              strokeWidth="6"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>

        <h1 className="subscription-successful-title">Subscription Cancelled</h1>

        <div className="subscription-details">
          <p className="mb-4">Your subscription process has been cancelled. No charges have been made to your account.</p>

          <div className="subscription-message mt-5">
            <h3 className="mb-3">
              <strong>What happens next?</strong>
            </h3>
            <p>
              You can try again or explore our other subscription plans that might better suit your needs. If you have any questions or concerns,
              please don't hesitate to contact our support team.
            </p>
          </div>
        </div>

        <div className="subscription-successful-buttons ">
          <Button className="dark-outline-btn rounded-md" onClick={handleGoToDashboard}>
            Go to Dashboard
          </Button>
          <Button className="primary-btn rounded-md" onClick={handleGoBack}>
            Try Again
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionCancel;
