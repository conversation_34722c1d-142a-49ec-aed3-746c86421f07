"use client";
import React, { FC, useState } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import InputWrapper from "../formElements/InputWrapper";
import Textarea from "../formElements/Textarea";
import { useForm } from "react-hook-form";
import GreenCheckIcon from "../svgComponents/GreenCheckIcon";
import RoundCrossIcon from "../svgComponents/RoundCrossIcon";
import { changeApplicationStatus } from "@/services/screenResumeServices";
import { useSelector } from "react-redux";
import { AuthState } from "@/redux/slices/authSlice";
import { JobApplication } from "@/interfaces/jobRequirementesInterfaces";
import { APPLICATION_STATUS } from "@/constants/jobRequirementConstant";
import Lottie from "lottie-react";
import rejected from "../../../public/assets/images/rejected.json";
import hurray from "../../../public/assets/images/hurray.json";
import { toastMessageSuccess, toTitleCase } from "@/utils/helper";

interface IProps {
  onClickCancel: () => void;
  disabled?: boolean;
  candidate?: JobApplication;
  aiDecision?: string;
  actionType: (typeof APPLICATION_STATUS)[keyof typeof APPLICATION_STATUS];
  onSuccess?: () => void;
  title?: string;
}

const CandidateStatusModal: FC<IProps> = ({ onClickCancel, candidate, actionType = APPLICATION_STATUS.APPROVED, onSuccess, aiDecision, title }) => {
  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<{ reason: string }>({
    defaultValues: {
      reason: "",
    },
    mode: "onSubmit",
    criteriaMode: "firstError",
    shouldFocusError: true,
    reValidateMode: "onChange",
    resolver: (values) => {
      const errors: Record<string, { type: string; message: string }> = {};

      // Required validation for reason field
      if (!values.reason || values.reason.trim() === "") {
        errors.reason = {
          type: "required",
          message: "Please provide a reason",
        };
      } else if (values.reason.trim().length < 5) {
        errors.reason = {
          type: "minLength",
          message: "Reason should be at least 5 characters long",
        };
      } else if (values.reason.trim().length > 50) {
        errors.reason = {
          type: "maxLength",
          message: "Reason should not exceed 50 characters",
        };
      }

      return {
        values,
        errors,
      };
    },
  });

  const onSubmit = async (formData: { reason: string }) => {
    if (!candidate || !authData) return;

    try {
      setIsSubmitting(true);
      setError("");

      const data = {
        job_id: candidate.job_id,
        candidate_id: candidate.candidate_id,
        hiring_manager_id: authData.id,
        status: actionType,
        hiring_manager_reason: formData.reason,
      };

      const response = await changeApplicationStatus(data);

      if (response.data && response.data.success) {
        let successMessage = "";

        switch (actionType) {
          case APPLICATION_STATUS.APPROVED:
            successMessage = "Candidate successfully marked as approved.";
            break;
          case APPLICATION_STATUS.REJECTED:
            successMessage = "Candidate successfully marked as rejected.";
            break;
          case APPLICATION_STATUS.ON_HOLD:
            successMessage = "Candidate has been placed on hold successfully!";
            break;
          default:
            successMessage = `Candidate status updated to ${actionType.toLowerCase()}.`;
        }

        toastMessageSuccess(successMessage);
        setSuccess(true);

        // Call the onSuccess callback if provided
        if (onSuccess) {
          setTimeout(() => {
            onClickCancel();
            onSuccess();
          }, 1500);
        }
      } else {
        setError(response.data?.message || "Failed to update candidate status");
      }
    } catch (err) {
      console.error("Error updating candidate status:", err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to determine modal header content
  const renderModalHeader = () => {
    if (actionType === APPLICATION_STATUS.APPROVED) {
      return (
        <>
          <h2 className="model-heading-lottie">
            Hurray! <Lottie animationData={hurray} className="lottie-icon" />
          </h2>
          <p>Candidate marked as a good fit, shortlisted for the next step.</p>
        </>
      );
    } else if (actionType === APPLICATION_STATUS.REJECTED) {
      return (
        <>
          <h2 className="model-heading-lottie">
            Uh-Oh! <Lottie animationData={rejected} className="lottie-icon" />
          </h2>
          <p>The candidate is unfit for the job.</p>
        </>
      );
    } else if (actionType === APPLICATION_STATUS.ON_HOLD) {
      return (
        <>
          <h2>On-Hold Confirmation</h2>
          <p>You are about to place this candidate on hold for further review.</p>
        </>
      );
    }
  };

  // Helper function to determine the status class
  const getStatusClass = () => {
    switch (actionType) {
      case APPLICATION_STATUS.APPROVED:
        return "approved-status";
      case APPLICATION_STATUS.REJECTED:
        return "approved-status rejected-status";
      case APPLICATION_STATUS.ON_HOLD:
        return "on-hold-status";
      default:
        return "";
    }
  };

  // Helper function to determine status label
  const getStatusLabel = () => {
    switch (actionType) {
      case APPLICATION_STATUS.APPROVED:
        return (
          <p>
            <GreenCheckIcon />
            {actionType} By You
          </p>
        );
      case APPLICATION_STATUS.REJECTED:
        return (
          <p>
            <RoundCrossIcon DangerColor />
            {actionType} By You
          </p>
        );
      case APPLICATION_STATUS.ON_HOLD:
        return <p>On-Hold For Review</p>;
      default:
        return <p>{actionType}</p>;
    }
  };

  // Helper function to determine reason label
  const getReasonLabel = () => {
    switch (actionType) {
      case APPLICATION_STATUS.APPROVED:
        return "Reason for approval";
      case APPLICATION_STATUS.REJECTED:
        return "Reason for rejection";
      case APPLICATION_STATUS.ON_HOLD:
        return "Reason for putting on-hold";
      default:
        return "Reason";
    }
  };

  // Helper function to determine reason placeholder
  const getReasonPlaceholder = () => {
    switch (actionType) {
      case APPLICATION_STATUS.APPROVED:
        return "Please enter the reason for approving this candidate";
      case APPLICATION_STATUS.REJECTED:
        return "Please enter the reason for rejecting this candidate";
      case APPLICATION_STATUS.ON_HOLD:
        return "Enter reason for putting candidate on-hold";
      default:
        return "Enter reason";
    }
  };

  // Helper for the AI reason title
  const getAiReasonTitle = () => {
    if (title) {
      return title;
    }

    if (actionType === APPLICATION_STATUS.REJECTED || aiDecision === APPLICATION_STATUS.REJECTED) {
      return "Why the candidate is unfit for the role";
    }

    return "Why the candidate is a good fit for the role";
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className={`modal-dialog modal-dialog-centered ${actionType === APPLICATION_STATUS.ON_HOLD ? "modal-md" : ""}`}>
        <div className="modal-content">
          <div className="modal-header justify-content-center pb-0">
            {renderModalHeader()}
            {!isSubmitting && (
              <Button className="modal-close-btn" onClick={onClickCancel}>
                <ModalCloseIcon />
              </Button>
            )}
          </div>
          <div className="modal-body">
            {/* qualification-card */}
            <div className="qualification-card">
              <div className="qualification-card-top">
                <div className="name">
                  <h3>{toTitleCase(candidate?.candidate_name || "Candidate")}</h3>
                  <p>{candidate?.ai_decision || "Pending"} by S9 InnerView</p>
                </div>
                <div className="top-right">
                  <div className={getStatusClass()}>{getStatusLabel()}</div>
                </div>
              </div>
              <div className="qualification-card-mid">
                <p>
                  <b>{getAiReasonTitle()}</b>
                </p>
                <p>{candidate?.ai_reason || "No reason provided by AI evaluation."}</p>
              </div>
            </div>

            {!success && (
              <form onSubmit={handleSubmit(onSubmit)}>
                <InputWrapper>
                  <InputWrapper.Label htmlFor="reason" required>
                    {getReasonLabel()}
                  </InputWrapper.Label>
                  <Textarea rows={4} name="reason" control={control} placeholder={getReasonPlaceholder()} className="form-control" />
                  <InputWrapper.Error message={errors?.reason?.message || ""} />
                </InputWrapper>

                {error && <div className="error-message alert alert-danger my-3">{error}</div>}

                <Button type="submit" className="primary-btn rounded-md w-100" disabled={isSubmitting}>
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CandidateStatusModal;
