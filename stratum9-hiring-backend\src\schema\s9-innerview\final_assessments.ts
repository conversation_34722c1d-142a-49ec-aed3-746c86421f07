import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  CreateDateC<PERSON>umn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { JobsModel } from "./jobs";
import JobApplicationsModel from "./job_applications";

@Entity("final_assessments")
export class FinalAssessmentsModel {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => JobsModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "job_id" })
  job: JobsModel;

  @Column({ name: "job_id", nullable: false })
  jobId: number;

  @ManyToOne(() => JobApplicationsModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "job_application_id" })
  jobApplication: JobApplicationsModel;

  @Column({ name: "job_application_id", nullable: false })
  jobApplicationId: number;

  @Column({ name: "is_assessment_submitted", nullable: true, default: false })
  isAssessmentSubmitted: boolean;

  @Column({ name: "is_assessment_shared", nullable: true, default: false })
  isAssessmentShared: boolean;

  @Column({
    name: "overall_success_probability",
    nullable: false,
    type: "float",
  })
  overallSuccessProbability: number;

  @Column({ name: "skill_summary", nullable: false, type: "json" })
  skillSummary: object;

  @Column({ name: "behavioural_scores", nullable: false, type: "json" })
  behaviouralScores: object;

  @Column({
    name: "development_recommendations",
    nullable: false,
    type: "json",
  })
  developmentRecommendations: object;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
    nullable: false,
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
    nullable: false,
  })
  updatedTs: Date;
}

export default FinalAssessmentsModel;
