import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from "typeorm";
import { ActivityLogType } from "../../utils/constants";

/**
 * Table: activity_logs
 * Audit log for tracking changes and actions in the system
 */
@Entity("activity_logs")
class ActivityLogModel {
  @PrimaryGeneratedColumn({ name: "audit_id" })
  auditId: number;

  @Column({ name: "org_id" })
  orgId: number;

  @Column({
    name: "log_type",
    type: "enum",
    enum: ActivityLogType,
    nullable: true,
  })
  logType: string;

  @Column({ name: "user_id" })
  userId: number;

  @Column({ name: "entity_id", nullable: true })
  entityId: number | null;

  @Column({ name: "entity_type", type: "varchar", length: 100, nullable: true })
  entityType: string | null;

  @Column({ name: "old_value", type: "text", nullable: true })
  oldValue: string | null;

  @Column({ name: "new_value", type: "text", nullable: true })
  newValue: string | null;

  @CreateDateColumn({ name: "timestamp", type: "timestamp" })
  timestamp: Date;

  @Column({ name: "comments", type: "text" })
  comments: string | null;

  @Column({ name: "action_by", type: "text" })
  actionBy: string;
}

export default ActivityLogModel;
