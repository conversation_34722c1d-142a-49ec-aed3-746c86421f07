import { FindOptionsWhere, In, Like, Repository } from "typeorm";
import * as Sentry from "@sentry/node";
import bcrypt from "bcryptjs";
import Cache from "../../db/cache";
import DbConnection from "../../db/dbConnection";
import { ResponseObject } from "../../interface/commonInterface";
import {
  ACCESS_MANAGEMENT_MSG,
  API_RESPONSE_MSG,
  DEFAULT_COUNTRY_CODE,
  EMPLOYEE_MANAGEMENT_MSG,
  REDIS_KEYS,
  USER_TYPE,
} from "../../utils/constants";

import DepartmentModel from "../../schema/s9-innerview/departments";
import { Employee } from "../../schema/s9-innerview/employees";
import RoleModel from "../../schema/s9-innerview/roles";
import UserModel from "../../schema/s9/user";
import OrganizationModel from "../../schema/s9/organization";
import AddressModel from "../../schema/s9/address";
import { JobsModel } from "../../schema/s9-innerview/jobs";
import JobApplicationsModel from "../../schema/s9-innerview/job_applications";
import InterviewModel from "../../schema/s9-innerview/interview";
import JobApplicationStatusHistoryModel from "../../schema/s9-innerview/job_application_status_history";

import * as helper from "../../utils/helper";
import sendNewEmployeeRegistrationMail, {
  EMPLOYEE_TYPE,
} from "../../utils/sendNewEmployeeRegisterationEmail";
import {
  IAddEmployee,
  IDepartmentData,
  IEmployeeInterviewOrderUpdate,
  IEmployeeQuery,
  IEmployeeRoleUpdate,
  IUpdateDepartmentData,
} from "./interface";
import { captureSentryError } from "../../config/sentryConfig";

class EmployeeManagementServices {
  static response: ResponseObject;

  /**
   * Get departments list
   * @param {number} organizationId - The organization ID to filter departments
   * @param {string} search - Optional search term to filter departments by name
   */
  static getDepartments = async (organizationId: number, search?: string) => {
    try {
      const departmentRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(DepartmentModel);

      const whereConditions: FindOptionsWhere<DepartmentModel> = {
        isActive: true,
        organizationId,
      };

      if (search) {
        whereConditions.name = Like(`%${search}%`);
      }

      const departments = await departmentRepo.find({
        where: whereConditions,
        order: { name: "ASC" },
        select: ["id", "name", "isDefaultDepartment"],
      });

      return {
        success: true,
        message: EMPLOYEE_MANAGEMENT_MSG.departments_fetch,
        data: departments,
      };
    } catch (error) {
      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
      };
    }
  };

  /**
   * Add a new department
   */
  static addDepartment = async (requestData: IDepartmentData) => {
    try {
      const { name, organizationId } = requestData;

      const departmentRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(DepartmentModel);

      // Check if department with the same name already exists in the organization
      const existingDepartment = await departmentRepo.findOne({
        where: {
          name,
          organizationId,
          isActive: true,
        } as FindOptionsWhere<DepartmentModel>,
      });

      if (existingDepartment) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.department_already_exists,
        };
      }

      // Create new department
      const newDepartment = new DepartmentModel();
      newDepartment.name = name;
      newDepartment.organizationId = organizationId;
      newDepartment.isActive = true;
      const savedDepartment = await departmentRepo.save(newDepartment);

      // user
      await DbConnection.getS9DataSource();
      return {
        success: true,
        message: EMPLOYEE_MANAGEMENT_MSG.department_added,
        data: {
          id: savedDepartment.id,
          name: savedDepartment.name,
          organizationId: savedDepartment.organizationId,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: EMPLOYEE_MANAGEMENT_MSG.add_failed,
      };
    }
  };

  /**
   * Delete a department
   * @param {string} departmentId - ID of the department to delete
   * @param {number} organizationId - Optional organization ID to verify department belongs to the organization
   * @returns {Promise<Object>} - Response object with success status and message
   */
  static deleteDepartment = async (
    departmentId: number,
    organizationId: number
  ) => {
    try {
      const departmentRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(DepartmentModel);

      // Always check orgId match during fetch
      const department = await departmentRepo.findOne({
        where: {
          id: departmentId,
          isActive: true,
          organizationId,
        },
      });

      if (!department) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.department_not_found,
        };
      }
      if (department.isDefaultDepartment) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.default_department_cannot_be_deleted,
        };
      }

      // Check for active employees in department
      const employeeRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(Employee);
      const employeeCount = await employeeRepo.count({
        where: {
          departmentId,
          isActive: true,
          organizationId,
        } as FindOptionsWhere<Employee>,
      });

      if (employeeCount > 0) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.department_has_employees,
        };
      }

      // Soft delete
      department.isActive = false;
      department.updatedTs = new Date();
      await departmentRepo.save(department);

      return {
        success: true,
        message: EMPLOYEE_MANAGEMENT_MSG.department_deleted,
        data: {
          id: department.id,
          name: department.name,
          organizationId: department.organizationId,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: EMPLOYEE_MANAGEMENT_MSG.delete_failed,
      };
    }
  };

  // Constants for search functionality
  static SEARCH_REGEX = /[^\w\s]/g;

  static SPLIT_SEARCH_REGEX = /\s+/;

  static REMOVE_SEARCH_REGEX = "\\$&";

  /**
   * Get employees by department with search and pagination
   * @description Optimized search functionality to search by first name, last name, and role name
   */
  static getEmployeesByDepartment = async (queryParams: IEmployeeQuery) => {
    try {
      const { departmentId, organizationId, search, limit, offset } =
        queryParams;

      // Validate required parameters
      if (!departmentId) {
        return {
          success: false,
          message: "Department ID is required",
        };
      }

      if (!organizationId) {
        return {
          success: false,
          message: "Organization ID is required",
        };
      }

      try {
        // Initialize repositories
        const employeeRepo: Repository<Employee> =
          await DbConnection.getS9InnerViewDatabaseRepository(Employee);
        const userRepo: Repository<UserModel> =
          await DbConnection.getS9DatabaseRepository(UserModel);

        // Simple approach: Get employees with their relations using find
        const employees = await employeeRepo.find({
          where: {
            departmentId,
            organizationId,
          },
          relations: ["department", "role"],
          order: {
            isActive: "DESC", // Active employees first
            interviewOrder: "ASC",
          },
        });

        if (!employees || employees.length === 0) {
          return {
            success: true,
            message: EMPLOYEE_MANAGEMENT_MSG.employees_fetch,
            data: [],
            pagination: {
              total: 0,
              offset,
              limit,
            },
          };
        }

        // Get all user IDs from employees
        const userIds = employees.map((e) => e.userId);

        // Get all related users
        const users = await userRepo.find({ where: { id: In(userIds) } });

        // Create a map for quick user lookups
        const userMap = new Map(users.map((user) => [user.id, user]));

        // Filter employees based on search criteria
        let filteredEmployees = employees;

        if (search?.trim()) {
          const searchTerms = search.toLowerCase().trim().split(/\s+/);

          filteredEmployees = employees.filter((employee) => {
            const user = userMap.get(employee.userId);
            if (!user) return false;

            // For each search term, check if any field matches
            return searchTerms.some((term) => {
              const firstName = user.first_name?.toLowerCase() || "";
              const lastName = user.last_name?.toLowerCase() || "";
              const roleName = employee.role?.name?.toLowerCase() || "";

              // Check each field for matches
              return (
                firstName.includes(term) ||
                lastName.includes(term) ||
                roleName.includes(term)
              );
            });
          });
        }

        // Get total count for pagination metadata
        const totalCount = filteredEmployees.length;

        // Apply pagination manually
        const paginatedEmployees = filteredEmployees.slice(
          offset,
          offset + limit
        );

        // Process output with user data
        const processedEmployees = paginatedEmployees.map((e) => {
          const user = userMap.get(e.userId);
          return {
            id: e.id,
            userId: e.userId,
            firstName: user?.first_name ?? "",
            lastName: user?.last_name ?? "",
            isActive: e?.isActive ?? "",
            email: user?.email ?? "",
            interviewOrder: e.interviewOrder,
            isAdmin: e.isAdmin,
            department: {
              id: e.department?.id,
              name: e.department?.name,
              isDefaultDepartment: e.department?.isDefaultDepartment,
            },
            selectedRole: {
              id: e.role?.id,
              name: e.role?.name,
              isDefaultRole: e.role?.isDefaultRole,
            },
          };
        });

        return {
          success: true,
          message: EMPLOYEE_MANAGEMENT_MSG.employees_fetch,
          data: processedEmployees,
          pagination: {
            total: totalCount,
            offset,
            limit,
          },
        };
      } catch (dbError) {
        // Log detailed DB error
        console.error("Database error in getEmployeesByDepartment:", dbError);
        Sentry.captureException(dbError);
        return {
          success: false,
          message: dbError.message || API_RESPONSE_MSG.fetch_failed,
        };
      }
    } catch (error) {
      // Log detailed error
      console.error("Error in getEmployeesByDepartment:", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
      };
    }
  };

  /**
   * Add employees with user creation functionality
   * This is the enhanced version based on the hireEmployeeRegistration feature
   */
  static addHireEmployees = async (
    requestData: IAddEmployee,
    orgId: number,
    assignedBy: number
  ) => {
    try {
      const { employees } = requestData;
      if (!employees?.length) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.no_employees_data,
        };
      }

      // Repositories
      const userRepo = await DbConnection.getS9DatabaseRepository(UserModel);
      const addrRepo = await DbConnection.getS9DatabaseRepository(AddressModel);
      const orgRepo =
        await DbConnection.getS9DatabaseRepository(OrganizationModel);

      const { departmentRepo, empRepo, roleRepo } =
        await helper.getRepositories({
          departmentRepo: DepartmentModel,
          empRepo: Employee,
          roleRepo: RoleModel,
        });

      const org = await orgRepo.findOne({ where: { id: orgId } });

      const results: Employee[] = [];
      const errors: { email: string; message: string }[] = [];
      const employeeStatuses: {
        email: string;
        status: boolean;
        message?: string;
      }[] = [];

      // Extract unique department IDs, role IDs, and emails from the request

      const {
        uniqueDepartmentIds: [...uniqueDepartmentIds],
        uniqueRoleIds: [...uniqueRoleIds],
        uniqueEmails: [...uniqueEmails],
      } = employees.reduce(
        (acc, emp) => {
          acc.uniqueDepartmentIds.add(emp.departmentId);
          acc.uniqueRoleIds.add(emp.roleId);
          acc.uniqueEmails.add(emp.email.toLowerCase());
          return acc;
        },
        {
          uniqueDepartmentIds: new Set<number>(),
          uniqueRoleIds: new Set<number>(),
          uniqueEmails: new Set<string>(),
        }
      );
      // const uniqueDepartmentIds = [
      //   ...new Set(employees.map((emp) => emp.departmentId)),
      // ];
      // const uniqueRoleIds = [...new Set(employees.map((emp) => emp.roleId))];
      // const uniqueEmails = [
      //   ...new Set(employees.map((emp) => emp.email.toLowerCase())),
      // ];

      // // Pre-fetch departments
      // const departmentsPromises = uniqueDepartmentIds.map(async (deptId) => {
      //   const dept = await departmentRepo.findOne({
      //     where: { id: deptId, organizationId: orgId },
      //   });
      //   return { deptId, dept };
      // });

      //   // Process the results for departments
      // const departmentsMap = new Map<number, DepartmentModel>();
      // const departmentResults = await Promise.all(departmentsPromises);
      // departmentResults.forEach(({ deptId, dept }) => {
      //   if (dept) {
      //     departmentsMap.set(deptId, dept);
      //   }
      // });

      const departments = await departmentRepo.find({
        where: {
          id: In(uniqueDepartmentIds),
          organizationId: orgId,
        },
      });

      if (
        !departments ||
        departments.length === 0 ||
        departments.length !== uniqueDepartmentIds.length
      ) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.please_select_valid_department,
        };
      }

      // const departmentsMap = new Map(departments.map(dept => [dept.id, dept]));

      //   // Pre-fetch roles
      //   const rolesPromises = uniqueRoleIds.map(async (roleId) => {
      //     const role = await roleRepo.findOne({
      //       where: { id: roleId, organizationId: orgId },
      //     });
      //     return { roleId, role };
      //   });

      // // Process the results for roles
      //       const rolesMap = new Map<number, RoleModel>();
      //       const roleResults = await Promise.all(rolesPromises);
      //       roleResults.forEach(({ roleId, role }) => {
      //         if (role) {
      //           rolesMap.set(roleId, role);
      //         }
      //       });

      // Pre-fetch roles in a single query using IN clause
      const roles = await roleRepo.find({
        where: {
          id: In(uniqueRoleIds),
          organizationId: orgId,
        },
      });

      if (
        !roles ||
        roles.length === 0 ||
        roles.length !== uniqueRoleIds.length
      ) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.please_select_valid_role,
        };
      }
      // Process the results and create a map of roles
      // const rolesMap = new Map(roles.map(role => [role.id, role]));

      // Pre-fetch existing users
      const existingUsers = await userRepo.find({
        where: { email: In(uniqueEmails) },
      });

      const userIds = [];

      const usersMap = new Map<string, UserModel>();

      existingUsers.forEach((user) => {
        userIds.push(user.id);
        usersMap.set(user.email.toLowerCase(), user);
      });

      // Pre-fetch existing employees for these users
      let existingEmployees = [];
      let existingAddresses = [];

      if (userIds.length) {
        existingEmployees = await empRepo.find({
          where: { userId: In(userIds), organizationId: orgId },
        });
        existingAddresses = await addrRepo.find({
          where: { user_id: In(userIds) },
        });
      }

      const employeesMap = new Map<number, Employee>(
        existingEmployees.map((emp) => [emp.userId, emp])
      );

      const addressesMap = new Map<number, AddressModel>(
        existingAddresses.map((addr) => [addr.user_id, addr])
      );

      // Pre-count employees in each department to track interview order
      // const countPromises = uniqueDepartmentIds.map(async (deptId) => {
      //   const count = await empRepo.count({
      //     where: { departmentId: deptId, organizationId: orgId },
      //   });
      //   return { deptId, count };
      // });

      // const countResults = await Promise.all(countPromises);
      // const departmentEmployeeCountMap = new Map<number, number>(countResults.map(({ deptId, count }) => [deptId, count]));

      const departmentStats = await empRepo
        .createQueryBuilder("emp")
        .select("emp.departmentId", "departmentId")
        .addSelect("MAX(emp.interview_order)", "maxOrder")
        .where("emp.organizationId = :orgId", { orgId })
        .andWhere("emp.departmentId IN (:...departmentIds)", {
          departmentIds: uniqueDepartmentIds,
        })
        .groupBy("emp.departmentId")
        .getRawMany();

      console.log(">>>>>>>>>>>>>>>>>>departmentStats", departmentStats);

      const departmentEmployeeCountMap = new Map<number, number>(
        departmentStats && departmentStats.length
          ? departmentStats.map(({ departmentId, maxOrder }) => [
              departmentId,
              maxOrder,
            ])
          : []
      );

      // Pre-validate all required data and collect errors for invalid entries
      // const invalidEmails = new Set<string>();

      const addressToCreate = [];
      const addressToUpdate = [];
      // const usersToCreate = [];
      const employeeToCreate = [];
      // Check for invalid roles and departments and other validations
      await Promise.all(
        employees.map(async (eData) => {
          const { firstName, lastName, email, roleId, departmentId } = eData;
          const lowerEmail = email.toLowerCase();

          // Check if user exists and is already registered as an employee
          let existingUser = usersMap.get(lowerEmail);

          if (existingUser && existingUser?.id) {
            const existingEmp = employeesMap.get(existingUser.id);

            if (existingEmp) {
              const isBelongsFromSameOrg =
                existingEmp.organizationId === org.id;
              errors.push({
                email,
                message: isBelongsFromSameOrg
                  ? EMPLOYEE_MANAGEMENT_MSG.employee_already_registered
                  : EMPLOYEE_MANAGEMENT_MSG.employee_already_registered_with_diff_org,
              });
              employeeStatuses.push({
                email,
                status: false,
                message: isBelongsFromSameOrg
                  ? EMPLOYEE_MANAGEMENT_MSG.employee_already_registered
                  : EMPLOYEE_MANAGEMENT_MSG.employee_already_registered_with_diff_org,
              });
              return null;
            }
            // Check if user belongs to a different organization
            const address = addressesMap.get(existingUser.id);
            const userOrgCode = address?.organization_code;
            if (userOrgCode && userOrgCode !== org.organization_code) {
              errors.push({
                email,
                message:
                  EMPLOYEE_MANAGEMENT_MSG.cannot_add_employee_user_registered_with_another_org,
              });
              employeeStatuses.push({
                email,
                status: false,
                message:
                  EMPLOYEE_MANAGEMENT_MSG.cannot_add_employee_user_registered_with_another_org,
              });
              return null;
            }
            if (!userOrgCode) {
              // Update address with organization info
              address.organization_code = org.organization_code;
              address.organization_id = orgId;
              addressToUpdate.push(address);

              // Also update user's first name and last name
              await userRepo.update(existingUser.id, {
                first_name: firstName,
                last_name: lastName,
              });
            }

            // Send email for existing users
            await sendNewEmployeeRegistrationMail({
              email,
              type: EMPLOYEE_TYPE.EXISTING,
              employeeName: `${firstName} ${lastName}`,
              organizationName: org.name,
            });
          } else {
            // Create new user
            const rawPassword = helper.generateRandomPassword();
            const encPassword = await bcrypt.hash(rawPassword, 10);
            existingUser = userRepo.create({
              email: email.toLocaleLowerCase(),
              password: encPassword,
              first_name: firstName ?? "",
              last_name: lastName ?? "",
              isVerified: true,
              account_type: USER_TYPE.user,
              created_ts: new Date(),
              updated_ts: new Date(),
              country: DEFAULT_COUNTRY_CODE,
              country_code: DEFAULT_COUNTRY_CODE,
            });
            await userRepo.save(existingUser);

            const newAddress = addrRepo.create({
              user_id: existingUser.id,
              organization_code: org.organization_code,
              organization_id: orgId,
            });

            addressToCreate.push(newAddress);

            await sendNewEmployeeRegistrationMail({
              email,
              type: EMPLOYEE_TYPE.NEW,
              employeeName: `${firstName} ${lastName}`,
              organizationName: org.name,
              rawPassword,
            });
          }

          const currentCount =
            departmentEmployeeCountMap.get(departmentId) || 0;
          const orderOfInterview = currentCount + 1;
          // Update the count in the map for future employees in the same department
          departmentEmployeeCountMap.set(departmentId, orderOfInterview);
          // 4) Create & save employee
          const employee = empRepo.create({
            userId: existingUser.id,
            organizationId: orgId,
            departmentId,
            roleId,
            assignedBy,
            isActive: true,
            isAdmin: false,
            interviewOrder: orderOfInterview,
          });

          try {
            employeeToCreate.push(employee);
            employeeStatuses.push({
              email,
              status: true,
              message: EMPLOYEE_MANAGEMENT_MSG.employee_added,
            });
          } catch (e: any) {
            Sentry.captureException(e);
            errors.push({ email, message: `DB error: ${e.message}` });
            employeeStatuses.push({
              email,
              status: false,
              message: `DB error: ${e.message}`,
            });
          }
          return null;
        })
      );

      await addrRepo.save(addressToUpdate);
      await addrRepo.insert(addressToCreate);
      await empRepo.insert(employeeToCreate);
      // Filter out invalid entries before processing
      // const validEmployees = employees.filter(
      //   (eData) => !invalidEmails.has(eData.email.toLowerCase())
      // );

      // Process only valid employees
      // await Promise.all(
      //   validEmployees.map(async (eData) => {
      //     const { firstName, lastName, email, roleId, departmentId } = eData;
      //     const lowerEmail = email.toLowerCase();

      //     // Role and department validation already done

      //     // Get and increment the interview order for this department
      //     const currentCount =
      //       departmentEmployeeCountMap.get(departmentId) || 0;
      //     const orderOfInterview = currentCount + 1;
      //     // Update the count in the map for future employees in the same department
      //     departmentEmployeeCountMap.set(departmentId, orderOfInterview);

      //     // Find or use pre-fetched user data
      //     let user = usersMap.get(lowerEmail);
      //     let address = null;

      //     if (user && user?.id) {
      //       // skip if already registered
      //       const existingEmp = await empRepo.findOne({
      //         where: { userId: user.id, organizationId: orgId },
      //       });
      //       if (existingEmp && existingEmp?.id) {
      //         errors.push({
      //           email,
      //           message: EMPLOYEE_MANAGEMENT_MSG.employee_already_registered,
      //         });
      //         employeeStatuses.push({
      //           email,
      //           status: false,
      //           message: EMPLOYEE_MANAGEMENT_MSG.employee_already_registered,
      //         });
      //         return; // Skip this iteration
      //       }

      //       let address = await addrRepo.findOne({
      //         where: { user_id: user.id },
      //       });
      //       // ensure address.organization_code matches
      //       // const userOrgCode = address?.organization_code;
      //       // if (userOrgCode && userOrgCode !== org.organization_code) {
      //       //   errors.push({
      //       //     email,
      //       //     message: EMPLOYEE_MANAGEMENT_MSG.cannot_add_employee,
      //       //   });
      //       //   employeeStatuses.push({
      //       //     email,
      //       //     status: false,
      //       //     message: EMPLOYEE_MANAGEMENT_MSG.cannot_add_employee,
      //       //   });
      //       //   return; // Skip this iteration
      //       // }

      //       if (!address) {
      //         address = addrRepo.create({
      //           user_id: user.id,
      //           organization_code: org.organization_code,
      //           organization_id: orgId,
      //         });
      //       } else {
      //         address.organization_code = org.organization_code;
      //         address.organization_id = orgId;
      //       }
      //       await addrRepo.save(address);

      //       // Send email for existing users
      //       await sendNewEmployeeRegistrationMail({
      //         email,
      //         type: EMPLOYEE_TYPE.EXISTING,
      //         employeeName: `${firstName} ${lastName}`,
      //         organizationName: org.name,
      //       });
      //     } else {
      //       // Create new user
      //       const rawPassword = helper.generateRandomPassword();
      //       const encPassword = await bcrypt.hash(rawPassword, 10);
      //       user = userRepo.create({
      //         email: email.toLocaleLowerCase(),
      //         password: encPassword,
      //         first_name: firstName ?? "",
      //         last_name: lastName ?? "",
      //         isVerified: true,
      //         account_type: USER_TYPE.user,
      //         created_ts: new Date(),
      //         updated_ts: new Date(),
      //         country: DEFAULT_COUNTRY_CODE,
      //         country_code: DEFAULT_COUNTRY_CODE,
      //       });
      //       await userRepo.save(user);

      //       const newAddress = addrRepo.create({
      //         user_id: user.id,
      //         organization_code: org.organization_code,
      //         organization_id: orgId,
      //       });
      //       await addrRepo.save(newAddress);

      //       await sendNewEmployeeRegistrationMail({
      //         email,
      //         type: EMPLOYEE_TYPE.NEW,
      //         employeeName: `${firstName} ${lastName}`,
      //         organizationName: org.name,
      //         rawPassword,
      //       });
      //     }

      //     // 4) Create & save employee
      //     const employee = empRepo.create({
      //       userId: user.id,
      //       organizationId: orgId,
      //       departmentId,
      //       roleId,
      //       assignedBy,
      //       isActive: true,
      //       isAdmin: false,
      //       interviewOrder: orderOfInterview,
      //     });

      //     try {
      //       const saved = await empRepo.save(employee);
      //       results.push(saved);
      //       employeeStatuses.push({
      //         email,
      //         status: true,
      //         message: EMPLOYEE_MANAGEMENT_MSG.employee_added,
      //       });
      //     } catch (e: any) {
      //       Sentry.captureException(e);
      //       errors.push({ email, message: `DB error: ${e.message}` });
      //       employeeStatuses.push({
      //         email,
      //         status: false,
      //         message: `DB error: ${e.message}`,
      //       });
      //     }
      //   })
      // );

      return {
        success: true,
        message: EMPLOYEE_MANAGEMENT_MSG.employees_added,
        data: {
          addedCount: results.length,
          employees: results,
          errors: errors.length ? errors : null,
          employeeStatuses,
        },
      };
    } catch (err) {
      console.log(err);
      Sentry.captureException(err);
      return { success: false, message: EMPLOYEE_MANAGEMENT_MSG.add_failed };
    }
  };

  /**
   * Update employee role
   */
  static updateEmployeeRole = async (
    employeeId: number,
    requestData: IEmployeeRoleUpdate
  ) => {
    try {
      const { roleId, organizationId } = requestData;
      const employeeRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(Employee);
      const roleRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(RoleModel);
      // Find the employee to update
      const employee = await employeeRepo.findOne({
        where: {
          id: employeeId,
          isActive: true,
          organizationId,
        },
      });

      if (!employee) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.employee_not_found,
        };
      }
      if (employee.isAdmin) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.cannot_update_role,
        };
      }

      // Verify role exists
      const role = await roleRepo.findOne({
        where: {
          id: roleId,
          isActive: true,
          organizationId: employee.organizationId,
        },
      });

      if (!role) {
        return {
          success: false,
          message: ACCESS_MANAGEMENT_MSG.role_not_found,
        };
      }

      // Update employee role
      const oldRoleId = employee.roleId;
      employee.roleId = roleId;
      await employeeRepo.save(employee);
      // If role has changed, invalidate all of the user's access tokens to force logout
      if (oldRoleId !== roleId) {
        try {
          // Invalidate all tokens for the user whose role was changed (not the admin's token)
          const cache = new Cache();
          const sessionKey = REDIS_KEYS.USER_SESSIONS.replace(
            "{userId}",
            String(employee.userId)
          );

          // Get all tokens for the user and delete them
          const userTokens = await cache.lRange(sessionKey);

          if (userTokens && userTokens.length > 0) {
            // Delete the entire redis key to remove all user tokens at once
            await cache.del(sessionKey);
          }
        } catch (error) {
          console.error("Error invalidating user tokens:", error);
          captureSentryError(error, "Error invalidating user tokens");
        }
      }

      return {
        success: true,
        message: EMPLOYEE_MANAGEMENT_MSG.employee_role_updated,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: EMPLOYEE_MANAGEMENT_MSG.update_failed,
      };
    }
  };

  /**
   * Delete an employee
   */
  static deleteEmployee = async (employeeId: number, orgId: number) => {
    try {
      const {
        jobsRepo,
        jobApplicationsRepo,
        interviewRepo,
        employeeRepo,
        jobAppStatusRepo,
      } = await helper.getRepositories({
        jobsRepo: JobsModel,
        jobApplicationsRepo: JobApplicationsModel,
        interviewRepo: InterviewModel,
        employeeRepo: Employee,
        jobAppStatusRepo: JobApplicationStatusHistoryModel,
      });

      // 1. Find employee (only once)
      const employee = await employeeRepo.findOne({
        where: { id: employeeId, isActive: true, organizationId: orgId },
        select: ["id", "userId"], // optimize select
      });

      if (!employee) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.employee_not_found,
        };
      }

      // Run all checks in parallel since they are independent
      const [activeJobs, activeApplications, futureInterviews, statusChanges] =
        await Promise.all([
          jobsRepo.exist({ where: { userId: employee.userId } }), // returns boolean
          jobApplicationsRepo.exist({
            where: { hiringManagerId: employee.userId, isActive: true },
          }),
          interviewRepo
            .createQueryBuilder("interview")
            .where("interview.interviewerId = :userId", {
              userId: employee.userId,
            })
            .orWhere("interview.scheduledBy = :userId", {
              userId: employee.userId,
            })
            .getExists(), // Use getExists instead of getOne for faster boolean check
          jobAppStatusRepo.exist({
            where: { changedByUserId: employee.userId },
          }),
        ]);

      if (activeJobs) {
        return {
          success: false,
          message:
            EMPLOYEE_MANAGEMENT_MSG.cannot_delete_employee_with_active_jobs,
        };
      }

      if (activeApplications) {
        return {
          success: false,
          message:
            EMPLOYEE_MANAGEMENT_MSG.cannot_delete_employee_with_active_job_applications,
        };
      }

      if (futureInterviews) {
        return {
          success: false,
          message:
            EMPLOYEE_MANAGEMENT_MSG.cannot_delete_employee_with_upcoming_interviews,
        };
      }

      if (statusChanges) {
        return {
          success: false,
          message:
            EMPLOYEE_MANAGEMENT_MSG.cannot_delete_employee_with_job_application_status_changes,
        };
      }

      // All checks passed, perform soft delete
      await employeeRepo.update(employee.id, { isActive: false });

      return {
        success: true,
        message: EMPLOYEE_MANAGEMENT_MSG.employee_deleted,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: EMPLOYEE_MANAGEMENT_MSG.delete_failed,
      };
    }
  };

  /**
   * Update employee status (isActive)
   * @param {number} employeeId - ID of the employee to update
   * @param {{ isActive: boolean, organizationId: number }} requestData - Data containing the new status and orgId
   * @returns {Promise<Object>} - Response object with success status and message
   */
  static updateEmployeeStatus = async (
    employeeId: number,
    requestData: { status: boolean; organizationId: number }
  ) => {
    try {
      const { status, organizationId } = requestData;
      const employeeRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(Employee);
      // Find the employee to update
      const employee = await employeeRepo.findOne({
        where: {
          id: employeeId,
          organizationId,
        },
      });
      if (!employee) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.employee_not_found,
        };
      }

      employee.isActive = status;
      employee.updatedTs = new Date();
      await employeeRepo.save(employee);

      // Invalidate all tokens for the user whose role was changed (not the admin's token)
      const cache = new Cache();
      const sessionKey = REDIS_KEYS.USER_SESSIONS.replace(
        "{userId}",
        String(employee.userId)
      );

      // Get all tokens for the user and delete them
      const userTokens = await cache.lRange(sessionKey);

      if (userTokens && userTokens.length > 0) {
        // Delete the entire redis key to remove all user tokens at once
        await cache.del(sessionKey);
      }
      return {
        success: true,
        message: EMPLOYEE_MANAGEMENT_MSG.employee_status_updated,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: EMPLOYEE_MANAGEMENT_MSG.update_failed,
      };
    }
  };

  /**
   * Update a department
   * @param {string} departmentId - ID of the department to update
   * @param {IUpdateDepartmentData} requestData - Data to update the department with
   * @returns {Promise<Object>} - Response object with success status, message, and updated data
   */
  static updateDepartment = async (
    departmentId: number,
    requestData: IUpdateDepartmentData
  ) => {
    try {
      const departmentRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(DepartmentModel);

      const department = await departmentRepo.findOne({
        where: {
          id: departmentId,
          organizationId: requestData.organizationId,
          isActive: true,
        },
      });

      if (!department) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.department_not_found,
        };
      }
      if (department.isDefaultDepartment) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.cannot_update_default_department,
        };
      }

      // Check for name conflicts if name is being updated
      if (requestData.name && requestData.name !== department.name) {
        const existingDepartment = await departmentRepo.findOne({
          where: {
            name: requestData.name,
            isActive: true,
            organizationId: requestData.organizationId,
          },
        });

        if (existingDepartment && existingDepartment.id !== departmentId) {
          return {
            success: false,
            message: EMPLOYEE_MANAGEMENT_MSG.department_already_exists,
          };
        }

        department.name = requestData.name;
      }

      department.updatedTs = new Date();
      const updatedDepartment = await departmentRepo.save(department);

      return {
        success: true,
        message: EMPLOYEE_MANAGEMENT_MSG.department_updated,
        data: {
          id: updatedDepartment.id,
          name: updatedDepartment.name,
          organizationId: updatedDepartment.organizationId,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: EMPLOYEE_MANAGEMENT_MSG.update_failed,
      };
    }
  };

  /**
   * Get departments by organization ID
   */
  static getDepartmentsByOrganizationId = async (organizationId: number) => {
    try {
      // Get departments by organization ID
      const departmentRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(DepartmentModel);
      const departments = await departmentRepo.find({
        where: {
          isActive: true,
          organizationId,
        },
        order: { name: "ASC" },
      });

      return {
        success: true,
        message: EMPLOYEE_MANAGEMENT_MSG.departments_by_organization_fetch,
        data: departments,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
      };
    }
  };

  /**
   * Update employee interview order
   * @param {number} employeeId - ID of the employee to update
   * @param {IEmployeeInterviewOrderUpdate} requestData - Data containing the new interview order
   * @returns {Promise<Object>} - Response object with success status and message
   */
  static updateEmployeeInterviewOrder = async (
    requestData: IEmployeeInterviewOrderUpdate
  ) => {
    try {
      const { employeeId, organizationId, newInterviewOrder, departmentId } =
        requestData;

      // Check if new interview order is valid (greater than 0)
      if (newInterviewOrder <= 0) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.invalid_sort_order,
        };
      }

      const employeeRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(Employee);

      // Find the employee to update
      const employee = await employeeRepo.findOne({
        where: {
          id: employeeId,
          organizationId,
          isActive: true,
        },
      });

      if (!employee) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.employee_not_found,
        };
      }

      // If the new order is the same as the current order, no need to update
      if (newInterviewOrder === employee.interviewOrder) {
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.same_as_current_order,
        };
      }

      // Begin transaction to ensure all updates are atomic
      const connection = await DbConnection.getS9InnerviewDataSource(); // however you obtain your DataSource
      const queryRunner = connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // CASE 1: Moving to a higher order (e.g., from 3 to 7)
        if (newInterviewOrder > employee.interviewOrder) {
          // Shift down employees with order > current order and <= new order
          await queryRunner.manager
            .createQueryBuilder()
            .update(Employee)
            .set({
              interviewOrder: () => "interview_order - 1",
              updatedTs: new Date(),
            })
            .where("organization_id = :organizationId", { organizationId })
            .andWhere("interview_order > :currentOrder", {
              currentOrder: employee.interviewOrder,
            })
            .andWhere("interview_order <= :newOrder", {
              newOrder: newInterviewOrder,
            })
            .andWhere("is_active = :isActive", { isActive: true })
            .execute();
        }
        // CASE 2: Moving to a lower order (e.g., from 5 to 2)
        else {
          // Shift up employees with order >= new order and < current order
          await queryRunner.manager
            .createQueryBuilder()
            .update(Employee)
            .set({
              interviewOrder: () => "interview_order + 1",
              updatedTs: new Date(),
            })
            .where("organization_id = :organizationId", { organizationId })
            .andWhere("interview_order >= :newOrder", {
              newOrder: newInterviewOrder,
            })
            .andWhere("interview_order < :currentOrder", {
              currentOrder: employee.interviewOrder,
            })
            .andWhere("is_active = :isActive", { isActive: true })
            .execute();
        }

        // Update the target employee's order
        await queryRunner.manager
          .createQueryBuilder()
          .update(Employee)
          .set({
            interviewOrder: newInterviewOrder,
            updatedTs: new Date(),
          })
          .where("id = :employeeId", { employeeId })
          .execute();

        // Commit the transaction
        await queryRunner.commitTransaction();

        // Get all affected employees - we know they're the ones between min and max of old and new order
        const minOrder = Math.min(employee.interviewOrder, newInterviewOrder);
        const maxOrder = Math.max(employee.interviewOrder, newInterviewOrder);

        // Get only the id and interviewOrder of affected employees
        const affectedEmployees = await queryRunner.manager
          .getRepository(Employee)
          .createQueryBuilder("employee")
          .select(["employee.id", "employee.interviewOrder"])
          .where("employee.organizationId = :organizationId", {
            organizationId,
          })
          .andWhere("employee.departmentId = :departmentId", { departmentId })
          .andWhere(
            "employee.interviewOrder >= :minOrder AND employee.interviewOrder <= :maxOrder",
            {
              minOrder,
              maxOrder,
            }
          )
          .andWhere("employee.isActive = :isActive", { isActive: true })
          .orderBy("employee.interviewOrder", "ASC")
          .getMany();

        // Format the response data to only include id and interviewOrder
        const simplifiedEmployees = affectedEmployees.map((emp) => ({
          id: emp.id,
          interviewOrder: emp.interviewOrder,
        }));

        return {
          success: true,
          message: EMPLOYEE_MANAGEMENT_MSG.employee_interview_order_updated,
          data: simplifiedEmployees,
        };
      } catch (transactionError) {
        // Rollback in case of error
        await queryRunner.rollbackTransaction();
        console.log("Transaction failed:", transactionError);
        Sentry.captureException(transactionError);
        return {
          success: false,
          message: EMPLOYEE_MANAGEMENT_MSG.error_updating_interview_order,
        };
      } finally {
        // Release the query runner
        await queryRunner.release();
      }
    } catch (error) {
      console.log("Transaction failed:", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: EMPLOYEE_MANAGEMENT_MSG.error_updating_interview_order,
      };
    }
  };

  static getEmployeesByOrganizationId = async (organizationId: number) => {
    try {
      const employeeRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(Employee);
      const userRepo = await DbConnection.getS9DatabaseRepository(UserModel);

      const employees = await employeeRepo
        .createQueryBuilder("employee")
        .leftJoinAndSelect("employee.department", "department")
        .leftJoinAndSelect("employee.role", "role")
        .select([
          "employee.id as id",
          "employee.userId as userId",
          "role.name as roleName",
          "department.name as departmentName",
          "employee.createdTs as createdTs",
        ])
        .where("employee.organizationId = :organizationId", { organizationId })
        .andWhere("employee.isActive = :isActive", { isActive: true })
        .getRawMany();

      // Collect all unique userIds
      const userIds = [...new Set(employees.map((i) => i.userId))];

      // Fetch all users in one query
      const users = await userRepo
        .createQueryBuilder("user")
        .leftJoinAndSelect("user.address", "address")
        .where("user.id IN (:...userIds)", { userIds })
        .select([
          "user.id as id",
          "user.first_name as firstName",
          "user.last_name as lastName",
          "user.email as email",
          "address.organization_id as organizationId",
        ])
        .getRawMany();

      // Map users by id for quick lookup
      const userMap = new Map(
        users.map((user) => [
          user.id,
          { name: `${user.firstName} ${user.lastName}`, email: user.email },
        ])
      );

      // Merge user info into employees
      const employeesInfo = employees.map((employee) => {
        const user = userMap.get(+employee.userId);
        return {
          ...employee,
          name: user.name || "",
          email: user.email || "",
        };
      });

      return {
        success: true,
        message: EMPLOYEE_MANAGEMENT_MSG.employees_fetch,
        data: employeesInfo,
      };
    } catch (error) {
      console.error("Error in getEmployeesByOrganizationId:", error);
      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
      };
    }
  };
}

export default EmployeeManagementServices;
