import { signOut } from "next-auth/react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";

import storage from "./storage";

import { ACCESS_TOKEN_KEY, PERMISSIONS_COOKIES_KEY } from "@/constants/commonConstants";
import { deleteSession } from "@/services/authServices";
import { getSignedUrl } from "@/services/commonService";
import { FilePath } from "@/interfaces/commonInterfaces";

export const getAccessToken = () => {
  return storage.get(ACCESS_TOKEN_KEY);
};

export const clearStorage = () => {
  return storage.removeAll();
};

export const setAccessToken = (accessToken: string) => {
  storage.set(ACCESS_TOKEN_KEY, accessToken);
};

/**
 * Toast style object
 */
const style = {
  fontSize: "16px",
};

/**
 * Toast success message
 * @param message - The message to display
 */
export const toastMessageSuccess = (message: string) => {
  toast.success(message, {
    style,
  });
};

/**
 * Toast success message with icon
 * @param message - The message to display
 * @param icon - The icon to display
 */
export const toastMessageWithIcon = (message: string, icon: string) => {
  toast.success(message, {
    style,
    icon,
  });
};

/**
 * Toast error message
 * @param message - The message to display
 */
export const toastMessageError = (message: string) => {
  toast.error(message, {
    style,
  });
};

/**
 * Dismiss all existing toast notifications
 */
export const dismissAllToasts = () => {
  toast.dismiss();
};

export const logout = async (userId?: number) => {
  try {
    deleteSession(userId);
    await signOut({ redirect: false });
    clearStorage();

    // Delete permissions_data cookies when user logs out
    Cookies.remove(PERMISSIONS_COOKIES_KEY, { path: "/" });
  } catch (error) {
    console.error("Error in logout:", error);
  }
};

/**
 *  get presignedUrl for image upload
 */
export const uploadFileOnS3 = async (file: Blob, filePath: string) => {
  let body: FilePath = {
    filePath: "",
    fileFormat: "",
  };
  body = {
    filePath,
    fileFormat: file.type as string,
  };
  let signedUrl;
  const presignedUrl = await getSignedUrl(body);
  if (presignedUrl && presignedUrl.data) {
    const response = await pushFileToS3(presignedUrl.data.data, file);
    if (response?.url) {
      signedUrl = response?.url.split("?")?.[0];
    }
  }

  return signedUrl?.replace(`${process.env.NEXT_PUBLIC_S3_URL}`, `${process.env.NEXT_PUBLIC_S3_CDN_URL}`);
};

/**
 *  Upload file on presignedUrl of S3
 */
export const pushFileToS3 = async (signedUrl: string, file: Blob): Promise<Response> => {
  return fetch(signedUrl, {
    method: "PUT",
    body: file,
    headers: {
      "Content-Type": file.type,
    },
  });
};

export const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};
// Format times as HH:MM for time inputs
export const formatTimeForInput = (date: Date) => {
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  return `${hours}:${minutes}`;
};

export const toTitleCase = (name: string) => {
  if (!name) return "";
  return name
    .toLowerCase()
    .split(" ")
    .filter((word) => word) // remove extra spaces
    .map((word) => word[0].toUpperCase() + word.slice(1))
    .join(" ");
};

// Normalize spaces (replace multiple spaces with a single space)
export const normalizeSpaces = (text: string): string => {
  return text.trim().replace(/\s+/g, " ");
};
